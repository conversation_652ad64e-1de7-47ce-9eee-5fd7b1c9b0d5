import { GeminiService } from '../services/geminiService';
import { GeminiJsonCodeResponse, FileNode, LicenseInfo, LicenseType } from '../types';

export class CoderAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("CoderAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Generates source code for a specific file based on project requirements.
   * @param projectIdea - The original project idea.
   * @param filePath - The path of the file to generate.
   * @param fileDescription - Description of what the file should contain.
   * @param projectContextString - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @param licenseInfo - Optional license information for the project.
   * @param clarifierResponse - Optional clarification response from the clarifier agent.
   * @returns A promise that resolves to the generated code response.
   */
  public async generateFileContent(
    projectIdea: string,
    filePath: string,
    fileDescription: string,
    projectContextString: string,
    fileStructure: FileNode[],
    modelName: string,
    licenseInfo?: LicenseInfo,
    clarifierResponse?: string
  ): Promise<GeminiJsonCodeResponse> {
    try {
      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      let licenseHeader = '';
      if (licenseInfo && licenseInfo.type !== LicenseType.Unspecified) {
          licenseHeader = `\n\nLicense Type: ${licenseInfo.type}. Include appropriate license header at the top of the file if needed.`;
      }

      const originalPrompt = `
        Project Idea: ${projectIdea}
        ${fileStructurePrompt}
        Project Context: ${projectContextString}
        File Path to Generate: ${filePath}
        File Description/Purpose: ${fileDescription}${licenseHeader}
        ${clarifierResponse ? `\nClarification Response: ${clarifierResponse}` : ''}
        Generate the complete code for this file.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Code Generator Agent. Generate complete, functional code for the specified file. Return a JSON object with 'code' property containing the generated code.",
        (data: any): data is GeminiJsonCodeResponse => {
          return typeof data === 'object' && data !== null &&
                 'code' in data && typeof data.code === 'string' &&
                 (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
        },
        0.5,
        true
      );

      return response;
    } catch (error) {
      console.error(`CoderAgent: Error generating file content for "${filePath}" -`, error);
      throw error;
    }
  }

  /**
   * Generates test code for a specific test file.
   * @param projectContext - The current project context.
   * @param testFilePath - The path of the test file to generate.
   * @param testFileDescription - Description of what the test file should test.
   * @param relatedSourceFiles - Array of related source file paths.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the generated test code response.
   */
  public async generateTestCode(
    projectContext: string,
    testFilePath: string,
    testFileDescription: string,
    relatedSourceFiles: string[],
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonCodeResponse> {
    try {
      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        Test File Path To Generate: ${testFilePath}
        Test File Description/Purpose: ${testFileDescription}
        Related Source Files: ${relatedSourceFiles.join(', ')}
        Generate the test code for this file.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Test Code Generator Agent. Generate comprehensive test code for the specified test file. Return a JSON object with 'code' property containing the test code.",
        (data: any): data is GeminiJsonCodeResponse => {
          return typeof data === 'object' && data !== null &&
                 'code' in data && typeof data.code === 'string' &&
                 (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
        },
        0.5,
        true
      );

      return response;
    } catch (error) {
      console.error(`CoderAgent: Error generating test code for "${testFilePath}" -`, error);
      throw error;
    }
  }
}
