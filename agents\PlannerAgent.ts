
import { GeminiService } from '../services/geminiService';
import { GeminiJsonPlannerResponse, UserFeedback, FileNode, LicenseInfo, GeminiJsonPlannerTask } from '../types';

export class PlannerAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("PlannerAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Generates the initial project plan, including tasks and file structure.
   * @param projectIdea - The user's idea for the project.
   * @param modelName - The name of the Gemini model to use.
   * @param projectContext - The current full context of the project.
   * @param licenseInfo - The chosen license information for the project.
   * @returns A promise that resolves to the planner's response.
   */
  public async generatePlan(
    projectIdea: string, 
    modelName: string,
    projectContext: string, // Added project context
    licenseInfo?: LicenseInfo    // Added license info
  ): Promise<GeminiJsonPlannerResponse> {
    try {
      // Pass projectContext and licenseInfo to the service method
      const plan = await this.geminiService.generateInitialPlan(projectIdea, modelName, projectContext, licenseInfo);
      return plan;
    } catch (error) {
      console.error("PlannerAgent: Error generating plan -", error);
      throw error;
    }
  }

  /**
   * Generates tasks based on user feedback.
   * @param projectContext - The current full context of the project.
   * @param projectIdea - The original project idea.
   * @param fileStructure - The current file structure.
   * @param feedback - The user's feedback.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to a planner response containing new tasks.
   */
  public async generateTasksFromFeedback(
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string
  ): Promise<GeminiJsonPlannerResponse> {
    try {
      const response = await this.geminiService.generateTasksFromUserFeedback(
        projectContext,
        projectIdea,
        fileStructure,
        feedback,
        modelName
      );
      // The response from generateTasksFromUserFeedback might only contain tasks.
      // Ensure it fits the GeminiJsonPlannerResponse structure, fileStructure might be undefined.
      return {
        tasks: response.tasks,
        fileStructure: response.fileStructure // This will likely be undefined as feedback planner focuses on tasks
      };
    } catch (error) {
      console.error("PlannerAgent: Error generating tasks from feedback -", error);
      throw error;
    }
  }

  /**
   * Reviews and refines an initial project plan.
   * @param projectIdea - The original project idea.
   * @param initialTasks - The initial tasks to review.
   * @param initialFileStructure - The initial file structure to review.
   * @param projectContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @param technologyStackSuggestion - Optional technology stack suggestion.
   * @returns A promise that resolves to the refined plan response.
   */
  public async reviewAndRefinePlan(
    projectIdea: string,
    initialTasks: GeminiJsonPlannerTask[],
    initialFileStructure: Array<Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }>,
    projectContext: string,
    modelName: string,
    technologyStackSuggestion?: string
  ): Promise<GeminiJsonPlannerResponse> {
    try {
      const response = await this.geminiService.reviewAndRefinePlan(
        projectIdea,
        initialTasks,
        initialFileStructure,
        projectContext,
        modelName,
        technologyStackSuggestion
      );
      return response;
    } catch (error) {
      console.error("PlannerAgent: Error reviewing and refining plan -", error);
      throw error;
    }
  }

  /**
   * Validates the project build configuration and identifies potential issues.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param packageJsonContent - The content of package.json file if available.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the build validation response.
   */
  public async validateProjectBuild(
    projectContext: string,
    fileStructure: FileNode[],
    packageJsonContent: string | undefined,
    modelName: string
  ): Promise<any> {
    try {
      const response = await this.geminiService.validateProjectBuild(
        projectContext,
        fileStructure,
        packageJsonContent,
        modelName
      );
      return response;
    } catch (error) {
      console.error("PlannerAgent: Error validating project build -", error);
      throw error;
    }
  }
}
