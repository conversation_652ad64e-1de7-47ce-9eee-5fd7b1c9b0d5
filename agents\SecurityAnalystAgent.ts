import { GeminiService } from '../services/geminiService';
import { GeminiJsonBugReportResponse, FileNode } from '../types';

export class SecurityAnalystAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("SecurityAnalystAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Analyzes code for potential security vulnerabilities.
   * @param code - The source code to analyze.
   * @param filePath - The path of the file being analyzed.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the security vulnerability report response.
   */
  public async analyzeCodeForSecurityVulnerabilities(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugReportResponse> {
    try {
      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        File Path Being Analyzed for Security: ${filePath}
        Code to analyze:
        \`\`\`
        ${code}
        \`\`\`
        Identify potential security vulnerabilities (e.g., XSS, SQLi, hardcoded secrets).
        Each vulnerability found MUST be returned as a BugInfo object with 'isSecurityIssue' set to true and appropriate 'severity' (usually 'high' or 'critical').
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Security Analyst Agent. Analyze the provided code for potential security vulnerabilities. Return a JSON object with 'bugs' array containing security issue objects with 'filePath', 'bugId', 'description', 'severity', and 'isSecurityIssue' set to true.",
        (data: any): data is GeminiJsonBugReportResponse => {
          return typeof data === 'object' && data !== null &&
                 'bugs' in data && Array.isArray(data.bugs) &&
                 data.bugs.every((b: any) =>
                   typeof b === 'object' && b !== null &&
                   'filePath' in b && typeof b.filePath === 'string' &&
                   'bugId' in b && typeof b.bugId === 'string' &&
                   'description' in b && typeof b.description === 'string' &&
                   'severity' in b && typeof b.severity === 'string' &&
                   ['low', 'medium', 'high', 'critical'].includes(b.severity) &&
                   (b.isSecurityIssue === true)
                 );
        },
        0.4
      );

      return response;
    } catch (error) {
      console.error(`SecurityAnalystAgent: Error analyzing code for security vulnerabilities in "${filePath}" -`, error);
      throw error;
    }
  }
}
