import { GeminiService } from '../services/geminiService';
import { GeminiJsonLintFormatResponse } from '../types';

export class LinterFormatterAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("LinterFormatterAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Lints and formats code to improve consistency and readability.
   * @param code - The source code to lint and format.
   * @param filePath - The path of the file being processed.
   * @param projectContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the lint and format response.
   */
  public async lintAndFormatCode(
    code: string,
    filePath: string,
    projectContext: string,
    modelName: string
  ): Promise<GeminiJsonLintFormatResponse> {
    try {
      const originalPrompt = `
        Project Context: ${projectContext}
        File Path: ${filePath}
        Code to lint and format:
        \`\`\`
        ${code}
        \`\`\`
        Refine the formatting and apply basic, safe linting corrections to this code. Do not change its logic.
        Return the improved code in the "lintedCode" field. Optionally, provide a brief "explanation".
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Linter and Formatter Agent. Improve the code formatting and apply safe linting corrections without changing logic. Return a JSON object with 'lintedCode' property containing the improved code.",
        (data: any): data is GeminiJsonLintFormatResponse => {
          return typeof data === 'object' && data !== null &&
                 'lintedCode' in data && typeof data.lintedCode === 'string' &&
                 (data.explanation === null || typeof data.explanation === 'undefined' || typeof data.explanation === 'string');
        },
        0.3,
        true // isCodeGenerationType should be true as output structure is similar ({lintedCode: "..."})
      );

      return response;
    } catch (error) {
      console.error(`LinterFormatterAgent: Error linting and formatting code for "${filePath}" -`, error);
      throw error;
    }
  }
}
