
import { GeminiService } from '../services/geminiService';
import { <PERSON><PERSON>ontext, GeminiJsonClarifierResponse } from '../types';
import { SYSTEM_INSTRUCTION_CLARIFIER } from '../constants';

export class ClarifierAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("ClarifierAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Provides clarification or makes a decision based on a question and project context.
   * This agent simulates user input or expert knowledge to unblock other agents.
   * @param question - The question posed by another agent.
   * @param projectContext - The current full context of the project.
   * @param modelName - The name of the Gemini model to use for clarification.
   * @returns A promise that resolves to the clarifier's answer.
   */
  public async clarify(
    question: string,
    projectContext: ProjectContext,
    modelName: string
  ): Promise<GeminiJsonClarifierResponse> {
    try {
      const response = await this.getClarification(question, projectContext, modelName);
      return response;
    } catch (error) {
      console.error(`ClarifierAgent: Error during clarification for question "${question}" -`, error);
      // Fallback response in case of error to maintain autonomy
      return {
        answer: `Error in ClarifierAgent: Could not get clarification for question: "${question.substring(0, 100)}...". Reason: ${error instanceof Error ? error.message : 'Unknown error'}. Attempting to proceed with a default or inferred behavior if possible.`,
        confidence: 0.1
      };
    }
  }

  /**
   * Internal method to get clarification from AI model
   */
  private async getClarification(
    question: string,
    projectContext: ProjectContext,
    modelName: string
  ): Promise<GeminiJsonClarifierResponse> {
    const originalPrompt = `
      Full Project Context:
      ---
      Project ID: ${projectContext.id}
      Project Name: ${projectContext.name}
      Original Idea: ${projectContext.idea}
      License: ${projectContext.licenseInfo?.type || 'Unspecified'}
      ${projectContext.licenseInfo?.authorship ? `Author: ${projectContext.licenseInfo.authorship.fullName}` : ''}
      Suggested Technology Stack: ${projectContext.suggestedTechnologyStack || 'Not yet defined'}
      Current Phase: ${projectContext.projectLifecycle}
      Current File Structure Overview:
      ${this.serializeFileStructureForPrompt(projectContext.fileStructure)}
      ---
      Full Context String (long-term memory dump):
      ${projectContext.fullContext.substring(0, 2000)}...
      ---
      Specific Question from another AI Agent:
      "${question}"
      ---
      Based on all the above context and the question, provide a concise, actionable answer.
      If the information is not explicit, make a reasonable inference or common-sense assumption.
      Avoid saying "I don't know." Your role is to enable the other agent to proceed.
    `;

    return (this.geminiService as any).makeRequestWithRetry(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_CLARIFIER,
      (data: any): data is GeminiJsonClarifierResponse => {
        return typeof data === 'object' && data !== null &&
               'answer' in data && typeof data.answer === 'string' &&
               (data.confidence === null || typeof data.confidence === 'undefined' || typeof data.confidence === 'number');
      },
      0.6
    );
  }

  /**
   * Helper method to serialize file structure for prompt
   */
  private serializeFileStructureForPrompt(fileStructure: any[], includeContent: boolean = false): string {
    let structureString = "Project File Structure Overview:\n";
    const traverse = (nodes: any[], depth: number) => {
      for (const node of nodes) {
        let typeInfo = node.type;
        if (node.isTestFile) typeInfo += ' (test file)';

        structureString += `${"  ".repeat(depth)}- ${node.path} (${typeInfo}${node.type === 'file' && !node.content ? ', empty' : node.type === 'file' && node.content ? ', has content' : ''})\n`;
        if (includeContent && node.type === 'file' && node.content) {
            const previewContent = node.content.substring(0, 300) + (node.content.length > 300 ? "..." : "");
            structureString += `${"  ".repeat(depth+1)}Content Preview:\n${"  ".repeat(depth+2)}${previewContent.split('\n').join(`\n${"  ".repeat(depth+2)}`)}\n`;
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children, depth + 1);
        }
      }
    };
    traverse(fileStructure, 0);
    return structureString;
  }
}
