import { GeminiService } from '../services/geminiService';
import { GeminiJsonContextUpdateResponse } from '../types';

export class ContextManagerAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("ContextManagerAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Updates the project context by integrating new information.
   * @param currentContext - The current project context.
   * @param newInformation - New information to integrate into the context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the updated context response.
   */
  public async updateProjectContext(
    currentContext: string,
    newInformation: string,
    modelName: string
  ): Promise<GeminiJsonContextUpdateResponse> {
    try {
      const originalPrompt = `
        Current Project Context:
        ---
        ${currentContext}
        ---
        New Information to integrate:
        ---
        ${newInformation}
        ---
        Update the project context.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Context Manager Agent. Update the project context by integrating new information. Return a JSON object with 'updatedContext' property containing the updated context.",
        (data: any): data is GeminiJsonContextUpdateResponse => {
            return typeof data === 'object' && data !== null &&
                   'updatedContext' in data && typeof data.updatedContext === 'string';
        },
        0.3
      );

      return response;
    } catch (error) {
      console.error("ContextManagerAgent: Error updating project context -", error);
      throw error;
    }
  }
}
