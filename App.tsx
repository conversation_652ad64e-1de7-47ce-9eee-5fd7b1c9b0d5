

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  ProjectContext,
  OperatingPhase,
  ProjectLifecyclePhase,
  Task,
  FileNode,
  AgentLog,
  TaskProcessingStage,
  TaskAgentMessage,
  AgentType,
  BugInfo,
  UserFeedback,
  SavedProjectEntry,
  UserAuthorshipDetails,
  LicenseInfo,
  LicenseType,
  TaskStatus
} from './types';
import { AgentModelConfigurationModal } from './components/ModelSelectionModal';
import { ApiKeyModal } from './components/ApiKeyModal';
import { RateLimitModal } from './components/RateLimitModal';
import { ProjectInputForm } from './components/ProjectInputForm';
import { UserFeedbackForm } from './components/UserFeedbackForm'; 
import { TaskListDisplay } from './components/TaskListDisplay';
import { FileExplorer } from './components/FileExplorer';
import { CodeViewer } from './components/CodeViewer';
import { AgentLogFeed } from './components/AgentLogFeed';
import { LoadingSpinner } from './components/LoadingSpinner';
import { ArchitecturalInsightsDisplay } from './components/ArchitecturalInsightsDisplay'; 
import { DecisionLogDisplay } from './components/DecisionLogDisplay';
import { ProjectProgressBar } from './components/ProjectProgressBar';
import { ResizablePanels } from './components/ResizablePanels'; 
import { SavedProjectsList } from './components/SavedProjectsList';
import { ConfirmModal } from './components/ConfirmModal';
import { LicenseChoiceModal } from './components/LicenseChoiceModal';
import { AuthorshipFormModal } from './components/AuthorshipFormModal';
import { GeminiService, RateLimitError } from './services/geminiService';
import { PlannerAgent } from './agents/PlannerAgent';
import { ClarifierAgent } from './agents/ClarifierAgent';
import { CoderAgent } from './agents/CoderAgent';
import { TesterAgent } from './agents/TesterAgent';
import { BugHunterAgent } from './agents/BugHunterAgent';
import { SecurityAnalystAgent } from './agents/SecurityAnalystAgent';
import { RefactorerAgent } from './agents/RefactorerAgent';
import { ContextManagerAgent } from './agents/ContextManagerAgent';
import { LinterFormatterAgent } from './agents/LinterFormatterAgent';
import { ImageGeneratorAgent } from './agents/ImageGeneratorAgent';
import {
  initialProjectContext,
  MAX_BUG_FIXING_CYCLES_PER_TASK,
  MAX_ATTEMPTS_PER_BUG,
  AVAILABLE_MODELS,
  DEFAULT_AGENT_MODEL_CONFIG,
  AGENT_MODEL_PREFERENCES,
  // REPETITIVE_BUG_THRESHOLD, // Not directly used in App.tsx logic for now
  // LOCAL_STORAGE_PROJECT_INDEX_KEY, // Will be replaced by backend calls
  // LOCAL_STORAGE_PROJECT_PREFIX, // Will be replaced by backend calls
  LOCAL_STORAGE_USER_AUTHORSHIP_KEY
} from './constants';
import { getAgentDisplayName } from './utils/displayUtils';
import { sanitizeFilename } from './utils/stringUtils';
import { v4 as uuidv4 } from 'uuid';
import JSZip from 'jszip';


// Helper function moved to top level for broader use
const findFileNodeByPath = (nodes: FileNode[], path: string): FileNode | null => {
  for (const node of nodes) {
    if (node.path === path) return node;
    if (node.children) {
      const foundInChild = findFileNodeByPath(node.children, path);
      if (foundInChild) return foundInChild;
    }
  }
  return null;
};

// Fully immutable updateFileContent
const updateFileContentImmutable = (nodes: FileNode[], path: string, newContent: string): FileNode[] => {
  return nodes.map(node => {
    if (node.path === path && node.type === 'file') {
      // If content is the same, return the original node to maintain reference equality
      if (node.content === newContent) return node;
      return { ...node, content: newContent };
    }
    if (node.children) {
      const newChildren = updateFileContentImmutable(node.children, path, newContent);
      // Only create new node if children actually changed
      if (newChildren !== node.children) {
        return { ...node, children: newChildren };
      }
    }
    return node;
  });
};

// Fully immutable addOrUpdateFileNode
const addOrUpdateFileNodeImmutable = (
  nodes: FileNode[],
  filePath: string,
  content: string,
  isTestFile: boolean = false
): FileNode[] => {
  const pathSegments = filePath.split('/');
  
  const recursiveUpdate = (currentNodes: FileNode[], segmentIndex: number, currentPathParts: string[]): FileNode[] => {
    if (segmentIndex >= pathSegments.length) {
      return currentNodes; 
    }

    const segment = pathSegments[segmentIndex];
    const nodePath = [...currentPathParts, segment].join('/');
    const existingNodeIndex = currentNodes.findIndex(n => n.name === segment);

    if (segmentIndex === pathSegments.length - 1) { // This is the file
      if (existingNodeIndex !== -1) { // File exists, update it
        const oldNode = currentNodes[existingNodeIndex];
        if (oldNode.type === 'file') {
          // If content and isTestFile status are the same, return original array to maintain reference
          if (oldNode.content === content && oldNode.isTestFile === isTestFile) return currentNodes;
          const updatedNode = { ...oldNode, content, isTestFile };
          const newNodes = [...currentNodes];
          newNodes[existingNodeIndex] = updatedNode;
          return newNodes;
        } else {
          console.error(`Path conflict: ${filePath} is a directory, cannot write file content.`);
          return currentNodes; 
        }
      } else { // File does not exist, add it
        return [
          ...currentNodes,
          { id: uuidv4(), name: segment, type: 'file', path: filePath, content, isTestFile },
        ];
      }
    } else { // This is a directory
      if (existingNodeIndex !== -1) { // Directory exists
        const oldNode = currentNodes[existingNodeIndex];
        if (oldNode.type === 'folder') {
          const newChildren = recursiveUpdate(oldNode.children || [], segmentIndex + 1, [...currentPathParts, segment]);
          if (newChildren === (oldNode.children || [])) return currentNodes; 

          const updatedNode = { ...oldNode, children: newChildren };
          const newNodes = [...currentNodes];
          newNodes[existingNodeIndex] = updatedNode;
          return newNodes;
        } else {
          console.error(`Path conflict: ${nodePath} is a file, cannot create subdirectory ${pathSegments[segmentIndex + 1]}.`);
          return currentNodes; 
        }
      } else { // Directory does not exist, create it and continue
        const newFolder: FileNode = {
          id: uuidv4(),
          name: segment,
          type: 'folder',
          path: nodePath,
          children: [],
          isTestFile: false,
        };
        newFolder.children = recursiveUpdate(newFolder.children || [], segmentIndex + 1, [...currentPathParts, segment]);
        return [...currentNodes, newFolder];
      }
    }
  };
  return recursiveUpdate([...nodes], 0, []); // Operate on a copy from the start
};


// Not used directly in App.tsx for now
// const simplifyDescriptionForPattern = (description: string): string => {
//   if (!description) return "generic_error";
//   return description
//     .toLowerCase()
//     .replace(/[^\w\s]/gi, '') 
//     .split(/\s+/) 
//     .slice(0, 7) 
//     .join('_'); 
// };

const MAX_TASK_AGENT_MESSAGES_IN_CONTEXT = 20; // For sending to backend, not for stripping for storage
const MAX_COMPANY_LOGS_IN_CONTEXT = 100;     // For sending to backend, not for stripping for storage
const IMAGE_DATA_PLACEHOLDER_FOR_STORAGE = "[Image data truncated for LocalStorage. Export project to see the full image.]";


const API_BASE_URL = 'http://localhost:3002/api'; // Backend server URL

export default function App(): React.ReactElement | null {
  const [project, setProject] = useState<ProjectContext>(initialProjectContext);
  const [geminiService, setGeminiService] = useState<GeminiService | null>(null);
  const [clarifierAgent, setClarifierAgent] = useState<ClarifierAgent | null>(null);
  const [coderAgent, setCoderAgent] = useState<CoderAgent | null>(null);
  const [testerAgent, setTesterAgent] = useState<TesterAgent | null>(null);
  const [bugHunterAgent, setBugHunterAgent] = useState<BugHunterAgent | null>(null);
  const [securityAnalystAgent, setSecurityAnalystAgent] = useState<SecurityAnalystAgent | null>(null);
  const [refactorerAgent, setRefactorerAgent] = useState<RefactorerAgent | null>(null);
  const [contextManagerAgent, setContextManagerAgent] = useState<ContextManagerAgent | null>(null);
  const [linterFormatterAgent, setLinterFormatterAgent] = useState<LinterFormatterAgent | null>(null);
  const [imageGeneratorAgent, setImageGeneratorAgent] = useState<ImageGeneratorAgent | null>(null);
  const [isFeedbackLoading, setIsFeedbackLoading] = useState(false);
  const [isProjectIdeaSubmitting, setIsProjectIdeaSubmitting] = useState(false); 
  const autonomousExecutionRunningRef = useRef(false);
  const [savedProjects, setSavedProjects] = useState<SavedProjectEntry[]>([]);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<string | null>(null); 
  const [userAuthorshipDetails, setUserAuthorshipDetails] = useState<UserAuthorshipDetails | null>(null);
  const [showLicenseChoiceModal, setShowLicenseChoiceModal] = useState(false);
  const [showAuthorshipFormModal, setShowAuthorshipFormModal] = useState(false);

  const addDecisionLogEntry = useCallback((
    agent: AgentType | 'System' | 'ProjectManager' | 'User', 
    action: string, 
    details: string, 
    reason?: string, 
    taskId?: string
  ) => {
    setProject(prev => ({
      ...prev,
      decisionLog: [
        ...prev.decisionLog,
        { id: uuidv4(), agent, action, details, reason, taskId, timestamp: new Date() }
      ]
    }));
  }, []);

  const addCompanyLog = useCallback(async (agent: string, message: string, status: AgentLog['status'], taskId?: string) => {
    const logEntry: AgentLog = { id: uuidv4(), timestamp: new Date(), agent, message, status, taskId };
    setProject(prev => ({
      ...prev,
      companyLogs: [...prev.companyLogs, logEntry]
    }));
    try {
      await fetch(`${API_BASE_URL}/logs`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(logEntry),
      });
    } catch (error) {
      console.error('Failed to send company log to backend:', error);
      // Optionally, queue logs to send later or handle error
    }
  }, []);
  
  const addTaskLog = useCallback((
    taskId: string, 
    agent: string, 
    message: string, 
    status: AgentLog['status'],
    stage?: TaskProcessingStage,
    subDetailSections?: TaskAgentMessage['subDetailSections']
  ) => {
    const taskLogEntry: TaskAgentMessage = { 
      id: uuidv4(), 
      timestamp: new Date(), 
      agent, 
      message, 
      status,
      stage,
      subDetailSections
    };
    setProject(prev => {
      const currentTaskForLog = prev.tasks.find(t => t.id === taskId);
      if(currentTaskForLog) {
         addCompanyLog(agent, `Task (${currentTaskForLog.description.substring(0,30)}...): ${message}`, status, taskId);
      } else {
         addCompanyLog(agent, `Task (ID: ${taskId}): ${message}`, status, taskId);
      }

      return {
        ...prev, 
        tasks: prev.tasks.map(t => 
          t.id === taskId 
            ? { ...t, agentMessages: [...t.agentMessages, taskLogEntry] }
            : t
        )
      };
    });
  }, [addCompanyLog]);

  const loadSavedProjects = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/projects`);
      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.statusText}`);
      }
      const projectIndex = await response.json() as SavedProjectEntry[];
      setSavedProjects(projectIndex.sort((a,b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()));
    } catch (e) {
      console.error("Error loading projects from backend:", e);
      addCompanyLog('System', `Error loading projects from backend: ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
      setSavedProjects([]);
    }
  }, [addCompanyLog]);
  
  const handleSaveCurrentProject = useCallback(async (projectToSave: ProjectContext) => {
    if (!projectToSave.id) {
      console.error("Project cannot be saved without an ID.");
      addCompanyLog('System', 'Save failed: Project ID is missing.', 'error');
      return;
    }

    const projectForApi = JSON.parse(JSON.stringify(projectToSave)) as ProjectContext;
    
    // Trim logs for context sent to backend if they are very large, but backend stores full logs separately.
    // The main ProjectContext on the backend might not need excessively long inline logs.
    projectForApi.tasks = projectForApi.tasks.map(task => {
      if (task.agentMessages && task.agentMessages.length > MAX_TASK_AGENT_MESSAGES_IN_CONTEXT) {
        task.agentMessages = task.agentMessages.slice(-MAX_TASK_AGENT_MESSAGES_IN_CONTEXT);
      }
      return task;
    });

    if (projectForApi.companyLogs && projectForApi.companyLogs.length > MAX_COMPANY_LOGS_IN_CONTEXT) {
      projectForApi.companyLogs = projectForApi.companyLogs.slice(-MAX_COMPANY_LOGS_IN_CONTEXT);
    }
    projectForApi.lastModified = new Date().toISOString();

    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectForApi.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectForApi),
      });

      if (!response.ok) {
        // If PUT fails (e.g. project doesn't exist to update), try POST to create new
        if (response.status === 404) {
          const postResponse = await fetch(`${API_BASE_URL}/projects`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(projectForApi),
          });
          if (!postResponse.ok) {
            throw new Error(`Failed to create project: ${postResponse.statusText}`);
          }
        } else {
          throw new Error(`Failed to save project: ${response.statusText}`);
        }
      }
      
      addCompanyLog('System', `Project '${projectForApi.name}' saved to backend.`, 'info');
      loadSavedProjects(); // Refresh the list of projects

    } catch (e) {
      console.error("Error saving project to backend:", e);
      const detailedErrorMessage = e instanceof Error ? e.message : 'Unknown error';
      setProject(prev => ({...prev, errorMessage: detailedErrorMessage}));
      addCompanyLog('System', `Error saving project '${projectForApi.name}' to backend: ${detailedErrorMessage}`, 'error');
    }
  }, [addCompanyLog, loadSavedProjects]);

  const getAgentTypeForStage = useCallback((stage: TaskProcessingStage | ProjectLifecyclePhase): AgentType | 'ProjectManager' | null => {
    switch (stage) {
        case 'CODING': return AgentType.CODER;
        case 'LINTING_FORMATTING': return AgentType.LINTER_FORMATTER;
        case 'SECURITY_ANALYSIS': return AgentType.SECURITY_ANALYST;
        case 'IMAGE_GENERATING': return AgentType.IMAGE_GENERATOR;
        case 'BUG_CHECKING':
        case 'AWAITING_BUG_RECHECK': return AgentType.BUG_HUNTER;
        case 'REFACTORING': return AgentType.REFACTORER;
        case 'CONTEXT_UPDATING': return AgentType.CONTEXT_MANAGER;
        case 'TEST_PLAN_GENERATING': 
        case ProjectLifecyclePhase.TESTING_PLANNING:
        case 'TEST_CODE_WRITING': 
        case ProjectLifecyclePhase.TEST_CODE_GENERATION_PENDING:
        case 'TEST_ANALYSIS_RUNNING': 
        case ProjectLifecyclePhase.TEST_ANALYSIS_PENDING:
        case ProjectLifecyclePhase.AWAITING_TEST_COVERAGE_ANALYSIS:
          return AgentType.TESTER;
        case 'CLARIFICATION_NEEDED': return AgentType.CLARIFIER; 
        case ProjectLifecyclePhase.PLANNING:
        case ProjectLifecyclePhase.PLAN_REVIEW_PENDING:
        case ProjectLifecyclePhase.PLANNING_USER_FEEDBACK:
        case ProjectLifecyclePhase.PROJECT_BUILD_VALIDATION: 
            return AgentType.PLANNER; 
        default: return null; 
    }
  }, []);
  
  const handleRateLimitError = useCallback((agentType: AgentType, modelId: string, errorMessage: string) => {
    addCompanyLog(
        `${getAgentDisplayName(agentType)} Agent`, 
        `Rate limit for model ${project.availableModels.find(m=>m.id === modelId)?.name || modelId}. Error: ${errorMessage.substring(0,100)}... Pausing.`, 
        'error'
    );
    addDecisionLogEntry(agentType, 'Rate Limit Encountered', `Model: ${modelId}. Error: ${errorMessage}`);
    
    const preferredModels = AGENT_MODEL_PREFERENCES[agentType] || [];
    const alternativeModelId = preferredModels.find(id => id !== modelId && project.availableModels.find(m => m.id === id));

    setProject(prev => {
      const updatedState = {
        ...prev,
        projectLifecycle: ProjectLifecyclePhase.AWAITING_RATE_LIMIT_RESOLUTION,
        rateLimitInfo: {
            agentType: agentType,
            modelId: modelId,
            errorMessage: errorMessage,
            proposedAlternativeModelId: alternativeModelId
        },
        activeTaskId: null 
      };
      if (prev.id) handleSaveCurrentProject(updatedState); 
      return updatedState;
    });
  }, [addCompanyLog, addDecisionLogEntry, project.availableModels, handleSaveCurrentProject]);


  useEffect(() => {
    const storedAgentConfigRaw = localStorage.getItem('agentModelConfiguration');
    let storedAgentConfig = DEFAULT_AGENT_MODEL_CONFIG;
    if (storedAgentConfigRaw) {
        try {
            const parsedConfig = JSON.parse(storedAgentConfigRaw) as Record<AgentType, string>;
            const allAgentKeys = Object.values(AgentType);
            let mergedConfig = { ...DEFAULT_AGENT_MODEL_CONFIG }; 

            allAgentKeys.forEach(agentKey => {
                if (parsedConfig[agentKey] && AVAILABLE_MODELS.find(m => m.id === parsedConfig[agentKey])) {
                    mergedConfig[agentKey] = parsedConfig[agentKey];
                } else if (!mergedConfig[agentKey] && AVAILABLE_MODELS.length > 0) {
                    const defaultModelForAgent = DEFAULT_AGENT_MODEL_CONFIG[agentKey];
                    if (defaultModelForAgent && AVAILABLE_MODELS.find(m => m.id === defaultModelForAgent)) {
                        mergedConfig[agentKey] = defaultModelForAgent;
                    } else {
                        mergedConfig[agentKey] = AVAILABLE_MODELS[0].id; 
                    }
                     addCompanyLog('System', `Model for agent ${agentKey} was missing/invalid, set to default.`, 'info');
                }
            });
            storedAgentConfig = mergedConfig;
            localStorage.setItem('agentModelConfiguration', JSON.stringify(storedAgentConfig));

        } catch (e) {
            addCompanyLog('System', 'Failed to parse agent model configuration, resetting to defaults.', 'error');
            localStorage.setItem('agentModelConfiguration', JSON.stringify(DEFAULT_AGENT_MODEL_CONFIG));
            storedAgentConfig = DEFAULT_AGENT_MODEL_CONFIG;
        }
    } else {
        localStorage.setItem('agentModelConfiguration', JSON.stringify(DEFAULT_AGENT_MODEL_CONFIG));
    }

    loadSavedProjects(); // Load from backend
    const storedApiKey = localStorage.getItem('geminiApiKey');
    const envApiKey = process.env.API_KEY;
    
    const storedAuthorshipDetails = localStorage.getItem(LOCAL_STORAGE_USER_AUTHORSHIP_KEY);
    if (storedAuthorshipDetails) {
        try {
            setUserAuthorshipDetails(JSON.parse(storedAuthorshipDetails));
        } catch (e) {
            console.error("Error parsing stored authorship details:", e);
            localStorage.removeItem(LOCAL_STORAGE_USER_AUTHORSHIP_KEY);
        }
    }


    setProject(prev => ({...prev, agentModelConfiguration: storedAgentConfig, availableModels: AVAILABLE_MODELS}));

    if (envApiKey) {
      localStorage.setItem('geminiApiKey', envApiKey);
      const service = new GeminiService(envApiKey);
      setGeminiService(service);
      setClarifierAgent(new ClarifierAgent(service));
      setCoderAgent(new CoderAgent(service));
      setTesterAgent(new TesterAgent(service));
      setBugHunterAgent(new BugHunterAgent(service));
      setSecurityAnalystAgent(new SecurityAnalystAgent(service));
      setRefactorerAgent(new RefactorerAgent(service));
      setContextManagerAgent(new ContextManagerAgent(service));
      setLinterFormatterAgent(new LinterFormatterAgent(service));
      setImageGeneratorAgent(new ImageGeneratorAgent(service));
      setProject(prev => ({ 
        ...prev, 
        apiKey: envApiKey, 
        currentPhase: OperatingPhase.COMPANY_OPERATIONAL, 
        projectLifecycle: ProjectLifecyclePhase.AWAITING_PROJECT_IDEA 
      }));
      addCompanyLog('System', `API Key from env. Agent models configured. Operational.`, 'success');
      addDecisionLogEntry('System', 'System Initialized', `API Key loaded from environment. Models configured from ${storedAgentConfigRaw ? 'storage (merged)' : 'defaults'}. Ready for project idea.`);
    } else if (storedApiKey) {
      const service = new GeminiService(storedApiKey);
      setGeminiService(service);
      setClarifierAgent(new ClarifierAgent(service));
      setCoderAgent(new CoderAgent(service));
      setTesterAgent(new TesterAgent(service));
      setBugHunterAgent(new BugHunterAgent(service));
      setSecurityAnalystAgent(new SecurityAnalystAgent(service));
      setRefactorerAgent(new RefactorerAgent(service));
      setContextManagerAgent(new ContextManagerAgent(service));
      setLinterFormatterAgent(new LinterFormatterAgent(service));
      setImageGeneratorAgent(new ImageGeneratorAgent(service));
      setProject(prev => ({ 
        ...prev, 
        apiKey: storedApiKey, 
        currentPhase: OperatingPhase.COMPANY_OPERATIONAL, 
        projectLifecycle: ProjectLifecyclePhase.AWAITING_PROJECT_IDEA 
      }));
      addCompanyLog('System', `API Key & Models from storage. Operational.`, 'success');
      addDecisionLogEntry('System', 'System Initialized', `API Key and Models loaded from local storage. Ready for project idea.`);
    } else {
      setProject(prev => ({ 
        ...prev, 
        currentPhase: OperatingPhase.AGENT_MODEL_CONFIGURATION, 
        projectLifecycle: ProjectLifecyclePhase.IDLE 
      }));
    }
  }, [addCompanyLog, addDecisionLogEntry, loadSavedProjects]); 

  const handleAgentModelConfigSubmit = (config: Record<AgentType, string>) => {
    localStorage.setItem('agentModelConfiguration', JSON.stringify(config));
    setProject(prev => ({
      ...prev,
      agentModelConfiguration: config,
      currentPhase: OperatingPhase.API_KEY_INPUT,
      errorMessage: undefined,
    }));
    addCompanyLog('System', `AI Agent models configured. Proceeding to API Key input.`, 'info');
    addDecisionLogEntry('System', 'Agent Models Configured', `User confirmed agent model assignments. New config: ${JSON.stringify(config)}`);
  };

  const handleApiKeySubmit = (apiKey: string) => {
    localStorage.setItem('geminiApiKey', apiKey);
    const service = new GeminiService(apiKey);
    setGeminiService(service);
    setClarifierAgent(new ClarifierAgent(service));
    setCoderAgent(new CoderAgent(service));
    setTesterAgent(new TesterAgent(service));
    setBugHunterAgent(new BugHunterAgent(service));
    setSecurityAnalystAgent(new SecurityAnalystAgent(service));
    setRefactorerAgent(new RefactorerAgent(service));
    setContextManagerAgent(new ContextManagerAgent(service));
    setLinterFormatterAgent(new LinterFormatterAgent(service));
    setImageGeneratorAgent(new ImageGeneratorAgent(service));
    setProject(prev => ({ 
      ...prev, 
      apiKey, 
      currentPhase: OperatingPhase.COMPANY_OPERATIONAL, 
      projectLifecycle: ProjectLifecyclePhase.AWAITING_PROJECT_IDEA, 
      errorMessage: undefined 
    }));
    addCompanyLog('System', `API Key set. Gemini Service initialized. Company operational.`, 'success');
    addDecisionLogEntry('System', 'API Key Submitted', 'Gemini API Key validated and services initialized.');
  };

  const handleCreateNewProject = async (idea: string) => {
    setIsProjectIdeaSubmitting(true); 
    const newProjectId = uuidv4();
    const newProjectName = idea.substring(0, 50) + (idea.length > 50 ? "..." : "");

    const newProjectContext: ProjectContext = {
      ...initialProjectContext,
      id: newProjectId,
      name: newProjectName,
      idea: idea,
      apiKey: project.apiKey, // Persist API key in context if already set
      agentModelConfiguration: project.agentModelConfiguration,
      availableModels: project.availableModels,
      currentPhase: OperatingPhase.COMPANY_OPERATIONAL,
      projectLifecycle: ProjectLifecyclePhase.AWAITING_LICENSE_CHOICE, 
      companyLogs: [{ id: uuidv4(), timestamp: new Date(), agent: 'System', message: `New Project Commissioned: ${idea}. Awaiting license selection.`, status: 'info' }],
      fullContext: `Project ID: ${newProjectId}\nProject Name: ${newProjectName}\nProject Idea: ${idea}.\nAwaiting license selection by user.`,
      architecturalNotes: `Initial project idea: ${idea}`,
      dependencyGraphNotes: 'No dependencies analyzed yet.',
      decisionLog: [{ 
          id: uuidv4(), 
          agent: 'ProjectManager', 
          action: 'Project Commissioned (Awaiting License)', 
          details: `Project Idea: "${idea}" (ID: ${newProjectId})`, 
          timestamp: new Date() 
      }],
      lastModified: new Date().toISOString(),
      licenseInfo: { type: LicenseType.Unspecified } 
    };
    
    // Attempt to save to backend immediately
    try {
        const response = await fetch(`${API_BASE_URL}/projects`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(newProjectContext),
        });
        if (!response.ok) {
            throw new Error(`Failed to create project on backend: ${response.statusText}`);
        }
        setProject(newProjectContext);
        addCompanyLog('System', `Project '${newProjectName}' created on backend. Now awaiting license selection.`, 'info');
        loadSavedProjects(); // Refresh list
        setShowLicenseChoiceModal(true); 

    } catch (e) {
        console.error("Error creating project on backend:", e);
        const errorMsg = e instanceof Error ? e.message : 'Unknown error creating project.';
        addCompanyLog('System', `Error creating project '${newProjectName}': ${errorMsg}`, 'error');
        setProject(prev => ({...prev, errorMessage: errorMsg, projectLifecycle: ProjectLifecyclePhase.AWAITING_PROJECT_IDEA }));
    } finally {
        setIsProjectIdeaSubmitting(false); 
    }
  };

  const handleLicenseChoiceSubmit = (choice: 'open-source' | 'proprietary') => {
    setShowLicenseChoiceModal(false);
    if (choice === 'open-source') {
      const licenseInfo: LicenseInfo = { type: LicenseType.MIT }; 
      setProject(prev => {
        const updatedProject = {
          ...prev,
          licenseInfo,
          projectLifecycle: ProjectLifecyclePhase.PLANNING,
          fullContext: `${prev.fullContext}\nLicense chosen: ${licenseInfo.type}.`
        };
        addDecisionLogEntry('User', 'License Choice Made', `User selected ${licenseInfo.type} license for project "${updatedProject.name}".`);
        addCompanyLog('System', `License set to ${licenseInfo.type}. Proceeding to project planning.`, 'info');
        if (updatedProject.id) handleSaveCurrentProject(updatedProject);
        return updatedProject;
      });
    } else { 
      if (userAuthorshipDetails) { 
        const licenseInfo: LicenseInfo = { 
          type: LicenseType.Proprietary, 
          authorship: userAuthorshipDetails 
        };
        setProject(prev => {
          const updatedProject = {
            ...prev,
            licenseInfo,
            projectLifecycle: ProjectLifecyclePhase.PLANNING,
            fullContext: `${prev.fullContext}\nLicense chosen: Proprietary. Using stored authorship: ${userAuthorshipDetails.fullName}.`
          };
          addDecisionLogEntry('User', 'License Choice Made', `User selected Proprietary license for project "${updatedProject.name}", using existing authorship details.`);
          addCompanyLog('System', `License set to Proprietary (using existing details). Proceeding to project planning.`, 'info');
          if (updatedProject.id) handleSaveCurrentProject(updatedProject);
          return updatedProject;
        });
      } else { 
        setShowAuthorshipFormModal(true);
      }
    }
  };

  const handleAuthorshipFormSubmit = (details: UserAuthorshipDetails) => {
    setUserAuthorshipDetails(details);
    localStorage.setItem(LOCAL_STORAGE_USER_AUTHORSHIP_KEY, JSON.stringify(details));
    setShowAuthorshipFormModal(false);

    const licenseInfo: LicenseInfo = { type: LicenseType.Proprietary, authorship: details };
    setProject(prev => {
      const updatedProject = {
        ...prev,
        licenseInfo,
        projectLifecycle: ProjectLifecyclePhase.PLANNING,
        fullContext: `${prev.fullContext}\nLicense chosen: Proprietary. Authorship details provided: ${details.fullName}.`
      };
      addDecisionLogEntry('User', 'Authorship Details Provided', `User submitted authorship details for Proprietary license on project "${updatedProject.name}". Full Name: ${details.fullName}.`);
      addCompanyLog('System', `Authorship details received. License set to Proprietary. Proceeding to project planning.`, 'info');
      if (updatedProject.id) handleSaveCurrentProject(updatedProject);
      return updatedProject;
    });
  };
  
  useEffect(() => {
    if (project.projectLifecycle === ProjectLifecyclePhase.PLANNING && project.id && project.licenseInfo?.type !== LicenseType.Unspecified && !autonomousExecutionRunningRef.current) {
        const planProject = async () => {
            if (autonomousExecutionRunningRef.current) return;
            autonomousExecutionRunningRef.current = true;

            if (!geminiService) {
                setProject(prev => ({ ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: 'Gemini service not initialized for planning.' }));
                addCompanyLog('System', 'Planning failed: Gemini Service not ready.', 'error');
                autonomousExecutionRunningRef.current = false;
                return;
            }

            const plannerModel = project.agentModelConfiguration[AgentType.PLANNER];
            const plannerAgent = new PlannerAgent(geminiService);
            
            addCompanyLog('Planner Agent', `Generating initial project plan for "${project.name}" with ${project.availableModels.find(m=>m.id === plannerModel)?.name || plannerModel}... (License: ${project.licenseInfo?.type})`, 'working');
            
            let updatedFullContext = project.fullContext;
            if (project.licenseInfo) {
                 updatedFullContext += `\nProject License: ${project.licenseInfo.type}.`;
                 if (project.licenseInfo.type === LicenseType.Proprietary && project.licenseInfo.authorship) {
                     updatedFullContext += ` Copyright Year: ${project.licenseInfo.authorship.copyrightYear}, Author: ${project.licenseInfo.authorship.fullName}.`;
                 }
            }

            try {
              const initialPlanResponse = await plannerAgent.generatePlan(project.idea, plannerModel, updatedFullContext, project.licenseInfo); 
              
              const techStackInfo = initialPlanResponse.technologyStackSuggestion 
                ? ` Tech Stack Suggestion: ${initialPlanResponse.technologyStackSuggestion}.` 
                : '';
              addCompanyLog('Planner Agent', `Initial plan generated for "${project.name}".${techStackInfo} Storing for AI review.`, 'info');
              addDecisionLogEntry(AgentType.PLANNER, 'Initial Plan Generated (Pre-Review)', `Tasks: ${initialPlanResponse.tasks.length}, Files: ${initialPlanResponse.fileStructure?.length || 0}.${techStackInfo} Awaiting AI review.`, undefined, project.id);

              setProject(prev => {
                const updatedProjectState = {
                  ...prev,
                  initialPlanForReview: { 
                      tasks: initialPlanResponse.tasks,
                      fileStructure: initialPlanResponse.fileStructure || [],
                      technologyStackSuggestion: initialPlanResponse.technologyStackSuggestion, 
                  },
                  suggestedTechnologyStack: initialPlanResponse.technologyStackSuggestion || prev.suggestedTechnologyStack, 
                  fullContext: updatedFullContext + 
                               (initialPlanResponse.technologyStackSuggestion ? `\nPlanner suggested technology stack: ${initialPlanResponse.technologyStackSuggestion}` : '') + 
                               `\nInitial plan generated, awaiting AI review. Tasks: ${initialPlanResponse.tasks.length}, Files: ${initialPlanResponse.fileStructure?.length || 0}.`,
                  projectLifecycle: ProjectLifecyclePhase.PLAN_REVIEW_PENDING,
                };
                if (prev.id) handleSaveCurrentProject(updatedProjectState);
                return updatedProjectState;
              });

            } catch (error) {
              console.error('Error generating initial plan:', error);
              const errorMessage = error instanceof Error ? error.message : String(error);
              addDecisionLogEntry(AgentType.PLANNER, 'Initial Plan Generation Failed', `Error: ${errorMessage}`, undefined, project.id);
              if (error instanceof RateLimitError) {
                handleRateLimitError(AgentType.PLANNER, error.modelId, errorMessage);
              } else {
                setProject(prev => {
                  const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Failed to generate plan: ${errorMessage}` };
                  if (prev.id) handleSaveCurrentProject(errorState);
                  return errorState;
                });
                addCompanyLog('Planner Agent', `Error generating plan: ${errorMessage}`, 'error');
              }
            } finally {
                autonomousExecutionRunningRef.current = false;
            }
        };
        planProject();
    }
  }, [project.projectLifecycle, project.id, project.licenseInfo, geminiService, project.agentModelConfiguration, project.idea, project.fullContext, addCompanyLog, addDecisionLogEntry, handleRateLimitError, handleSaveCurrentProject, project.availableModels, project.name]);


  const handleLoadProject = useCallback(async (projectId: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}`);
      if (!response.ok) {
        throw new Error(`Failed to load project from backend: ${response.statusText}`);
      }
      const loadedProject = await response.json() as ProjectContext;
      
      const service = new GeminiService(loadedProject.apiKey!); 
      setGeminiService(service);
      setClarifierAgent(new ClarifierAgent(service));

      setProject(loadedProject);
      addCompanyLog('System', `Project '${loadedProject.name}' loaded successfully from backend.`, 'success');
      addDecisionLogEntry('User', 'Project Loaded', `Project "${loadedProject.name}" (ID: ${projectId}) loaded from backend.`);
      if (loadedProject.projectLifecycle === ProjectLifecyclePhase.PROJECT_PAUSED_ON_RATE_LIMIT) {
         addCompanyLog('System', `Project '${loadedProject.name}' was paused due to a rate limit. Review model configurations or wait.`, 'info');
      }
    } catch (e) {
      console.error(`Error loading project ${projectId} from backend:`, e);
      const errorMsg = e instanceof Error ? e.message : 'Unknown error';
      addCompanyLog('System', `Error loading project ID ${projectId} from backend: ${errorMsg}.`, 'error');
      setProject(prev => ({ ...prev, errorMessage: `Failed to load project ${projectId}. It might be corrupted or not found.`}));
      loadSavedProjects(); // Attempt to refresh list if a specific load failed
    }
  }, [addCompanyLog, addDecisionLogEntry, loadSavedProjects]);

  const handleDeleteProject = useCallback(async (projectId: string) => {
    const projectToDelete = savedProjects.find(p => p.id === projectId);
    if (!projectToDelete) return;

    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}`, { method: 'DELETE' });
      if (!response.ok) {
        throw new Error(`Failed to delete project from backend: ${response.statusText}`);
      }
      addCompanyLog('System', `Project '${projectToDelete.name}' (ID: ${projectId}) deleted from backend.`, 'info');
      addDecisionLogEntry('User', 'Project Deleted', `Project "${projectToDelete.name}" (ID: ${projectId}) deleted from backend.`);
      loadSavedProjects(); // Refresh the list

      if (project.id === projectId) {
        // Reset to initial state but keep API key and config if they exist
        const currentApiKey = project.apiKey;
        const currentAgentConfig = project.agentModelConfiguration;
        const currentModels = project.availableModels;

        setProject({
          ...initialProjectContext,
          apiKey: currentApiKey,
          agentModelConfiguration: currentAgentConfig,
          availableModels: currentModels,
          currentPhase: currentApiKey ? OperatingPhase.COMPANY_OPERATIONAL : OperatingPhase.AGENT_MODEL_CONFIGURATION,
          projectLifecycle: currentApiKey ? ProjectLifecyclePhase.AWAITING_PROJECT_IDEA : ProjectLifecyclePhase.IDLE,
        });
        const storedAuthorship = localStorage.getItem(LOCAL_STORAGE_USER_AUTHORSHIP_KEY);
        setUserAuthorshipDetails(storedAuthorship ? JSON.parse(storedAuthorship) : null);
      }
    } catch (e) {
      console.error(`Error deleting project ${projectId} from backend:`, e);
      addCompanyLog('System', `Error deleting project '${projectToDelete.name}': ${e instanceof Error ? e.message : 'Unknown error'}`, 'error');
    } finally {
      setShowDeleteConfirmModal(null);
    }
  }, [savedProjects, project.id, project.apiKey, project.agentModelConfiguration, project.availableModels, addCompanyLog, addDecisionLogEntry, loadSavedProjects]);
  
  
  useEffect(() => {
    const validExecutionPhases = [
        ProjectLifecyclePhase.PLAN_REVIEW_PENDING, 
        ProjectLifecyclePhase.AUTONOMOUS_EXECUTION,
        ProjectLifecyclePhase.TESTING_PLANNING,
        ProjectLifecyclePhase.TEST_ANALYSIS_PENDING,
        ProjectLifecyclePhase.AWAITING_TEST_COVERAGE_ANALYSIS,
        ProjectLifecyclePhase.PROJECT_BUILD_VALIDATION,
        ProjectLifecyclePhase.PLANNING_USER_FEEDBACK, 
    ];

    if (!validExecutionPhases.includes(project.projectLifecycle) || autonomousExecutionRunningRef.current || !project.id) { 
      return;
    }
    
    if (project.projectLifecycle === ProjectLifecyclePhase.AWAITING_LICENSE_CHOICE && project.id) {
      autonomousExecutionRunningRef.current = false; 
      return;
    }

    const processNextStep = async () => {
      if (autonomousExecutionRunningRef.current) return;
      autonomousExecutionRunningRef.current = true;
      
      let currentProjectSnapshot = { ...project }; 


      try {
        if (!geminiService) throw new Error("GeminiService is not available.");
        
        let nextTask = currentProjectSnapshot.tasks.find(t => t.status === 'pending' && t.currentProcessingStage !== 'DONE');
        if (!nextTask && currentProjectSnapshot.tasks.some(t => t.status === 'in-progress')) {
            nextTask = currentProjectSnapshot.tasks.find(t => t.status === 'in-progress');
        }
        const activeTask = nextTask || currentProjectSnapshot.tasks.find(t => t.id === currentProjectSnapshot.activeTaskId);


        if (activeTask && activeTask.currentProcessingStage === 'CLARIFICATION_NEEDED' && activeTask.clarificationQuestion && !activeTask.clarifierResponse && clarifierAgent) {
            const agentTypeForCall = AgentType.CLARIFIER;
            const modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
            if (!modelForCall) throw new Error(`Model not configured for Clarifier Agent.`);

            addTaskLog(activeTask.id, getAgentDisplayName(agentTypeForCall), `AI Clarifier attempting to answer: "${activeTask.clarificationQuestion}"`, 'working', 'CLARIFICATION_NEEDED');
            try {
                const clarification = await clarifierAgent.clarify(activeTask.clarificationQuestion, currentProjectSnapshot, modelForCall);
                addTaskLog(activeTask.id, getAgentDisplayName(agentTypeForCall), `Clarification obtained: "${clarification.answer.substring(0,100)}..."`, 'success', 'CLARIFICATION_PROVIDED', [{title: "Clarification", content: clarification.answer}]);
                setProject(prev => {
                    const updatedProject = {
                        ...prev,
                        tasks: prev.tasks.map(t => t.id === activeTask.id ? {
                            ...t,
                            clarifierResponse: clarification.answer,
                            currentProcessingStage: t.originalStageBeforeClarification || ('CODING' as TaskProcessingStage), 
                            status: 'pending' as TaskStatus 
                        } : t)
                    };
                    if (prev.id) handleSaveCurrentProject(updatedProject);
                    return updatedProject;
                });
            } catch (e) {
                const errorMsg = e instanceof Error ? e.message : String(e);
                addTaskLog(activeTask.id, getAgentDisplayName(agentTypeForCall), `Error getting clarification: ${errorMsg}`, 'error', 'CLARIFICATION_NEEDED');
                setProject(prev => {
                     const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === activeTask.id ? { ...t, status: 'error' as TaskStatus, error: `Clarification failed: ${errorMsg}` } : t) };
                     if (prev.id) handleSaveCurrentProject(updatedProject);
                     return updatedProject;
                });
                if (e instanceof RateLimitError) handleRateLimitError(agentTypeForCall, e.modelId, errorMsg);
            }
            autonomousExecutionRunningRef.current = false;
            return;
        }


        switch (currentProjectSnapshot.projectLifecycle) {
            case ProjectLifecyclePhase.PLAN_REVIEW_PENDING:
                if (currentProjectSnapshot.initialPlanForReview) {
                    const agentTypeForCall = AgentType.PLANNER; 
                    const modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    addCompanyLog(getAgentDisplayName(agentTypeForCall), `Reviewing initial project plan using ${currentProjectSnapshot.availableModels.find(m=>m.id === modelForCall)?.name || modelForCall}...`, 'working');
                    try {
                        const reviewedPlanResponse = await (new PlannerAgent(geminiService!)).reviewAndRefinePlan(
                            currentProjectSnapshot.idea,
                            currentProjectSnapshot.initialPlanForReview.tasks,
                            currentProjectSnapshot.initialPlanForReview.fileStructure,
                            currentProjectSnapshot.fullContext,
                            modelForCall,
                            currentProjectSnapshot.initialPlanForReview.technologyStackSuggestion
                        );

                        const newTasks = reviewedPlanResponse.tasks.map(t => ({
                            ...t,
                            id: t.id || uuidv4(), 
                            status: 'pending' as TaskStatus,
                            currentProcessingStage: 'QUEUED' as TaskProcessingStage,
                            agentMessages: [], identifiedBugs: [], unresolvedBugs: [], bugFixingCycles: 0,
                        }));

                        const newFileStructure = (reviewedPlanResponse.fileStructure || []).map(n => ({
                            ...n, id: uuidv4(), path: n.name, 
                            children: n.children?.map(c => ({...c, id: uuidv4(), path: `${n.name}/${c.name}`})) || []
                        })) as FileNode[];
                        
                        const planSummary = `Plan Review Summary: ${reviewedPlanResponse.reviewNotes || 'No specific review notes from AI.'}`;
                        const techStackMessage = reviewedPlanResponse.technologyStackSuggestion ? `\nReviewed/Confirmed technology stack: ${reviewedPlanResponse.technologyStackSuggestion}` : '\nNo technology stack suggestion after review.';
                        
                        addDecisionLogEntry(AgentType.PLANNER, 'Plan Reviewed & Finalized', `${planSummary}${techStackMessage}`, undefined, currentProjectSnapshot.id);
                        addCompanyLog(getAgentDisplayName(agentTypeForCall), 'Initial plan reviewed and finalized by AI.', 'success');
                        
                        setProject(prev => {
                            const updatedProjectState = { 
                                ...prev,
                                tasks: newTasks,
                                fileStructure: newFileStructure,
                                projectLifecycle: ProjectLifecyclePhase.AUTONOMOUS_EXECUTION,
                                fullContext: prev.fullContext + techStackMessage + `\n${planSummary}`,
                                initialPlanForReview: undefined, 
                                suggestedTechnologyStack: reviewedPlanResponse.technologyStackSuggestion || prev.suggestedTechnologyStack,
                                architecturalSuggestions: reviewedPlanResponse.reviewNotes ? [...prev.architecturalSuggestions, `Planner Review: ${reviewedPlanResponse.reviewNotes}`] : prev.architecturalSuggestions,
                                activeTaskId: null, 
                            }; 
                            if (prev.id) handleSaveCurrentProject(updatedProjectState);
                            return updatedProjectState;
                        });

                    } catch (error) {
                        const err = error instanceof Error ? error : new Error(String(error));
                        addCompanyLog(getAgentDisplayName(agentTypeForCall), `Error reviewing plan: ${err.message}`, 'error');
                        addDecisionLogEntry(agentTypeForCall, 'Plan Review Failed', `Error: ${err.message}`, err.stack, currentProjectSnapshot.id);
                        if (err instanceof RateLimitError) handleRateLimitError(agentTypeForCall, err.modelId, err.message);
                        else {
                           setProject(prev => { 
                             const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Plan review failed: ${err.message}` };
                             if (prev.id) handleSaveCurrentProject(errorState);
                             return errorState;
                           });
                        }
                    }
                } else {
                     addCompanyLog('System', 'Plan review pending, but no initial plan found. Halting.', 'error');
                     setProject(prev => {
                        const errorState = {...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: 'Cannot review plan: Initial plan data missing.'};
                        if (prev.id) handleSaveCurrentProject(errorState);
                        return errorState;
                     });
                }
                break;
            case ProjectLifecyclePhase.PLANNING_USER_FEEDBACK:
                 if (currentProjectSnapshot.lastUserFeedback) {
                    const agentTypeForCall = AgentType.PLANNER;
                    const modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    addCompanyLog(getAgentDisplayName(agentTypeForCall), `Generating tasks from user feedback: "${currentProjectSnapshot.lastUserFeedback.description.substring(0, 50)}..."`, 'working');
                    try {
                        const feedbackPlanResponse = await (new PlannerAgent(geminiService!)).generateTasksFromFeedback(
                            currentProjectSnapshot.fullContext,
                            currentProjectSnapshot.idea,
                            currentProjectSnapshot.fileStructure,
                            currentProjectSnapshot.lastUserFeedback,
                            modelForCall
                        );
                        const newFeedbackTasks = feedbackPlanResponse.tasks.map(t => ({
                            ...t,
                            id: t.id || uuidv4(),
                            status: 'pending' as TaskStatus,
                            currentProcessingStage: 'QUEUED' as TaskProcessingStage,
                            agentMessages: [], identifiedBugs: [], unresolvedBugs: [], bugFixingCycles: 0,
                            purpose: 'user-feedback-driven' 
                        }));
                        
                        addCompanyLog(getAgentDisplayName(agentTypeForCall), `${newFeedbackTasks.length} new task(s) generated from user feedback.`, 'success');
                        addDecisionLogEntry(agentTypeForCall, 'Tasks Generated from User Feedback', `Generated ${newFeedbackTasks.length} tasks to address user feedback. First task: ${newFeedbackTasks[0]?.description.substring(0,50) || 'N/A'}...`, undefined, currentProjectSnapshot.id);

                        setProject(prev => {
                            const updatedProjectState = {
                                ...prev,
                                tasks: [...prev.tasks, ...newFeedbackTasks],
                                projectLifecycle: ProjectLifecyclePhase.AUTONOMOUS_EXECUTION,
                                lastUserFeedback: undefined, // Clear feedback after processing
                                activeTaskId: null,
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProjectState);
                            return updatedProjectState;
                        });

                    } catch (error) {
                        const err = error instanceof Error ? error : new Error(String(error));
                        addCompanyLog(getAgentDisplayName(agentTypeForCall), `Error generating tasks from user feedback: ${err.message}`, 'error');
                        addDecisionLogEntry(agentTypeForCall, 'Feedback Task Generation Failed', `Error: ${err.message}`, err.stack, currentProjectSnapshot.id);
                        if (err instanceof RateLimitError) handleRateLimitError(agentTypeForCall, err.modelId, err.message);
                        else {
                           setProject(prev => {
                             const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Failed to process user feedback: ${err.message}` };
                             if (prev.id) handleSaveCurrentProject(errorState);
                             return errorState;
                           });
                        }
                    }
                } else {
                    addCompanyLog('System', 'In PLANNING_USER_FEEDBACK phase, but no user feedback found. Moving to AUTONOMOUS_EXECUTION to pick up other tasks or complete.', 'info');
                     setProject(prev => {
                        const nextState = {...prev, projectLifecycle: ProjectLifecyclePhase.AUTONOMOUS_EXECUTION };
                        if (prev.id) handleSaveCurrentProject(nextState);
                        return nextState;
                     });
                }
                break;

          case ProjectLifecyclePhase.AUTONOMOUS_EXECUTION:
            if (!activeTask) {
                const allTasksDone = currentProjectSnapshot.tasks.every(t => t.status === 'completed');
                if (allTasksDone && currentProjectSnapshot.tasks.length > 0) {
                    addCompanyLog('Project Manager', 'All primary tasks completed. Initiating testing phase.', 'info');
                    setProject(prev => {
                        const updatedProject = { ...prev, projectLifecycle: ProjectLifecyclePhase.TESTING_PLANNING, activeTaskId: null };
                        if (prev.id) handleSaveCurrentProject(updatedProject);
                        return updatedProject;
                    });
                } else if (currentProjectSnapshot.tasks.length === 0 && currentProjectSnapshot.projectLifecycle === ProjectLifecyclePhase.AUTONOMOUS_EXECUTION) {
                    addCompanyLog('Project Manager', 'No tasks found after planning. Moving to verification or error state.', 'info');
                    setProject(prev => {
                        const updatedProject = { ...prev, projectLifecycle: ProjectLifecyclePhase.PROJECT_BUILD_VALIDATION, activeTaskId: null };
                        if (prev.id) handleSaveCurrentProject(updatedProject);
                        return updatedProject;
                    });
                } else {
                   // Wait for next tick or if a task becomes pending
                }
                break; 
            }
            
            setProject(prev => ({...prev, activeTaskId: activeTask.id, tasks: prev.tasks.map(t => t.id === activeTask.id ? {...t, status: 'in-progress' as TaskStatus} : t) }));

            let currentTask = currentProjectSnapshot.tasks.find(t => t.id === activeTask.id)!; 
            let agentTypeForCall: AgentType | null = null;
            let modelForCall: string = '';
            
            switch (currentTask.currentProcessingStage) {
                case 'QUEUED':
                    addTaskLog(currentTask.id, 'Project Manager', `Starting task: ${currentTask.description}`, 'info', 'QUEUED');
                    setProject(prev => {
                        const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentProcessingStage: currentTask.description.startsWith("Generate image:") ? ('IMAGE_GENERATING' as TaskProcessingStage) : ('CODING' as TaskProcessingStage) } : t) };
                        if (prev.id) handleSaveCurrentProject(updatedProject);
                        return updatedProject;
                    });
                    break;
                case 'IMAGE_GENERATING':
                    agentTypeForCall = AgentType.IMAGE_GENERATOR;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Generating image for "${currentTask.description}" targeting "${currentTask.details}"...`, 'working', 'IMAGE_GENERATING');
                    try {
                        const imagePrompt = currentTask.description.replace(/^Generate image:\s*/i, '');
                        const imageData = await imageGeneratorAgent!.generateImage(imagePrompt, modelForCall);

                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Image generated successfully for ${currentTask.details}.`, 'success', 'IMAGE_GENERATED', [{title: "Generated Image", content: `<img src="${imageData.dataUrl}" alt="Generated image for ${currentTask.details}" style="max-width: 200px; max-height: 200px; border: 1px solid #555;"/>`}]);
                        setProject(prev => {
                            const updatedFileStructure = addOrUpdateFileNodeImmutable(prev.fileStructure, currentTask.details!, imageData.dataUrl);
                            const updatedProject = {
                                ...prev,
                                fileStructure: updatedFileStructure,
                                tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, code: imageData.dataUrl, currentProcessingStage: 'CONTEXT_UPDATING' as TaskProcessingStage } : t),
                                currentFilePreview: findFileNodeByPath(updatedFileStructure, currentTask.details!) || prev.currentFilePreview
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });

                    } catch (err) {
                        const errorMsg = err instanceof Error ? err.message : String(err);
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error generating image for ${currentTask.details}: ${errorMsg}`, 'error', 'IMAGE_GENERATING');
                        addDecisionLogEntry(agentTypeForCall, 'Image Generation Failed', `File: ${currentTask.details}, Error: ${errorMsg}`, err instanceof Error ? err.stack : undefined, currentTask.id);
                        if (err instanceof RateLimitError) {
                             handleRateLimitError(agentTypeForCall, err.modelId, errorMsg);
                        } else {
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Image generation failed: ${errorMsg}` } : t), activeTaskId: null };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    }
                    break;
                case 'CODING':
                    agentTypeForCall = AgentType.CODER;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    if (currentTask.details && currentTask.details.includes(',')) {
                        const errorMsg = `Task "${currentTask.description}" is malformed. The 'details' field "${currentTask.details}" contains multiple file paths. Each file must be handled by a separate task.`;
                        addTaskLog(currentTask.id, 'Project Manager', errorMsg, 'error', 'FAILED');
                        addDecisionLogEntry('ProjectManager', 'Malformed Task Error', errorMsg, undefined, currentTask.id);
                        setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: errorMsg, currentProcessingStage: 'FAILED' as TaskProcessingStage } : t), activeTaskId: null };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                        break; 
                    }
                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Generating code for ${currentTask.details || currentTask.description}...`, 'working', 'CODING');
                    try {
                        let codeResponse;
                        if (currentTask.purpose === 'generate-test-code') {
                            if (!currentTask.details || !currentTask.description || !currentTask.relatedSourceFiles) {
                                throw new Error("Missing details, description, or relatedSourceFiles for test code generation task.");
                            }
                            codeResponse = await testerAgent!.generateTestCode(
                                currentProjectSnapshot.fullContext,
                                currentTask.details,
                                currentTask.description,
                                currentTask.relatedSourceFiles,
                                currentProjectSnapshot.fileStructure,
                                modelForCall
                            );
                        } else {
                             codeResponse = await coderAgent!.generateFileContent(
                                currentProjectSnapshot.idea, currentTask.details!, currentTask.description, currentProjectSnapshot.fullContext, currentProjectSnapshot.fileStructure, modelForCall, currentProjectSnapshot.licenseInfo, currentTask.clarifierResponse
                            );
                        }

                        if (codeResponse.clarificationQuestion) {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Needs clarification: ${codeResponse.clarificationQuestion}`, 'info', 'CLARIFICATION_NEEDED');
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, clarificationQuestion: codeResponse.clarificationQuestion, originalStageBeforeClarification: 'CODING' as TaskProcessingStage, currentProcessingStage: 'CLARIFICATION_NEEDED' as TaskProcessingStage, status: 'pending' as TaskStatus } : t)};
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        } else {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Code generated for ${currentTask.details}. Length: ${codeResponse.code.length}`, 'success', 'CODE_GENERATED', [{title: "Generated Code Snippet", content: codeResponse.code.substring(0, 500) + (codeResponse.code.length > 500 ? "..." : ""), isCodeBlock: true}]);
                            setProject(prev => {
                                const isTestFile = currentTask.purpose === 'generate-test-code';
                                const updatedFileStructure = addOrUpdateFileNodeImmutable(prev.fileStructure, currentTask.details!, codeResponse.code, isTestFile);
                                const updatedProject = {
                                    ...prev,
                                    fileStructure: updatedFileStructure,
                                    tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, code: codeResponse.code, currentProcessingStage: 'LINTING_FORMATTING' as TaskProcessingStage, clarifierResponse: undefined } : t),
                                    currentFilePreview: findFileNodeByPath(updatedFileStructure, currentTask.details!) || prev.currentFilePreview
                                };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    } catch (err) { 
                        const errorMsg = err instanceof Error ? err.message : String(err);
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error generating code for ${currentTask.details}: ${errorMsg}`, 'error', 'CODING');
                        addDecisionLogEntry(agentTypeForCall, 'Code Generation Failed', `File: ${currentTask.details}, Error: ${errorMsg}`, err instanceof Error ? err.stack : undefined, currentTask.id);
                        if (err instanceof RateLimitError) {
                             handleRateLimitError(agentTypeForCall, err.modelId, errorMsg);
                        } else {
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Code generation failed: ${errorMsg}` } : t), activeTaskId: null };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    }
                    break;
                case 'LINTING_FORMATTING':
                    agentTypeForCall = AgentType.LINTER_FORMATTER;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    let codeToLint = currentTask.code;
                    if (!codeToLint && currentTask.details) { 
                        const fileNode = findFileNodeByPath(currentProjectSnapshot.fileStructure, currentTask.details);
                        if (fileNode && fileNode.content) codeToLint = fileNode.content;
                    }
                    if (!codeToLint) {
                         addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Skipping linting: No code found for ${currentTask.details}. Moving to Security Analysis.`, 'info', 'LINTING_FORMATTING');
                         setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentProcessingStage: 'SECURITY_ANALYSIS' as TaskProcessingStage } : t)};
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                         });
                         break;
                    }
                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Linting & formatting ${currentTask.details}...`, 'working', 'LINTING_FORMATTING');
                    try {
                        const lintResponse = await linterFormatterAgent!.lintAndFormatCode(codeToLint, currentTask.details!, currentProjectSnapshot.fullContext, modelForCall);
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Linting & formatting complete for ${currentTask.details}. ${lintResponse.explanation || ''}`, 'success', 'LINTING_FORMATTING', [{title: "Linting/Formatting Changes", content: lintResponse.explanation || "Code formatted for consistency and readability.", isCodeBlock: false}]);
                        setProject(prev => {
                             const updatedFileStructure = updateFileContentImmutable(prev.fileStructure, currentTask.details!, lintResponse.lintedCode);
                             const updatedProject = {
                                ...prev,
                                fileStructure: updatedFileStructure,
                                tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, code: lintResponse.lintedCode, currentProcessingStage: 'SECURITY_ANALYSIS' as TaskProcessingStage } : t),
                                currentFilePreview: findFileNodeByPath(updatedFileStructure, currentTask.details!) || prev.currentFilePreview
                             };
                             if (prev.id) handleSaveCurrentProject(updatedProject);
                             return updatedProject;
                        });
                    } catch (error) { 
                        const errorMsg = error instanceof Error ? error.message : String(error);
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error linting/formatting ${currentTask.details}: ${errorMsg}`, 'error', 'LINTING_FORMATTING');
                        addDecisionLogEntry(agentTypeForCall, 'Linting/Formatting Failed', `File: ${currentTask.details}, Error: ${errorMsg}`, error instanceof Error ? error.stack : undefined, currentTask.id);
                        if (error instanceof RateLimitError) {
                             handleRateLimitError(agentTypeForCall, error.modelId, errorMsg);
                        } else {
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Linting/formatting failed: ${errorMsg}` } : t), activeTaskId: null };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    }
                    break;
                case 'SECURITY_ANALYSIS':
                    agentTypeForCall = AgentType.SECURITY_ANALYST;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    let codeToAnalyzeSec = currentTask.code;
                     if (!codeToAnalyzeSec && currentTask.details) {
                        const fileNode = findFileNodeByPath(currentProjectSnapshot.fileStructure, currentTask.details);
                        if (fileNode && fileNode.content) codeToAnalyzeSec = fileNode.content;
                    }
                    if (!codeToAnalyzeSec) {
                         addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Skipping security analysis: No code found for ${currentTask.details}. Moving to Bug Checking.`, 'info', 'SECURITY_ANALYSIS');
                         setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentProcessingStage: 'BUG_CHECKING' as TaskProcessingStage } : t)};
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                         });
                         break;
                    }
                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Performing security analysis on ${currentTask.details}...`, 'working', 'SECURITY_ANALYSIS');
                    try {
                        const securityReport = await securityAnalystAgent!.analyzeCodeForSecurityVulnerabilities(codeToAnalyzeSec, currentTask.details!, currentProjectSnapshot.fullContext, currentProjectSnapshot.fileStructure, modelForCall);
                        const securityBugs = securityReport.bugs.map(b => ({...b, isSecurityIssue: true, attempts: 0, bugId: b.bugId || `sec-${uuidv4()}`}));
                        
                        if (securityBugs.length > 0) {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Security analysis found ${securityBugs.length} issue(s) in ${currentTask.details}.`, 'info', 'SECURITY_ANALYSIS', securityBugs.map(b => ({ title: `Security Issue: ${b.bugId}`, content: `Severity: ${b.severity}\nPath: ${b.filePath}\nDesc: ${b.description}${b.lineNumber ? `\nLine: ${b.lineNumber}`: ''}` })));
                            addDecisionLogEntry(agentTypeForCall, 'Security Issues Identified', `Found ${securityBugs.length} vulnerabilities in ${currentTask.details}. First: ${securityBugs[0].description.substring(0,50)}...`, undefined, currentTask.id);
                        } else {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `No security vulnerabilities found in ${currentTask.details}. Proceeding to general bug check.`, 'success', 'SECURITY_ANALYSIS');
                        }
                        setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, identifiedBugs: [...(t.identifiedBugs || []), ...securityBugs], currentProcessingStage: 'BUG_CHECKING' as TaskProcessingStage } : t)};
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                    } catch (error) { 
                        const err = error instanceof Error ? error : new Error(String(error));
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error during security analysis for ${currentTask.details}: ${err.message}`, 'error', 'SECURITY_ANALYSIS');
                        addDecisionLogEntry(agentTypeForCall, 'Security Analysis Failed', `File: ${currentTask.details}, Error: ${err.message}`, err.stack, currentTask.id);
                         if (err instanceof RateLimitError) {
                             handleRateLimitError(agentTypeForCall, err.modelId, err.message);
                        } else {
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Security analysis failed: ${err.message}` } : t), activeTaskId: null };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                     }
                    break;
                case 'BUG_CHECKING':
                    agentTypeForCall = AgentType.BUG_HUNTER;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];

                    const fileNodeForBugCheck = findFileNodeByPath(currentProjectSnapshot.fileStructure, currentTask.details!);
                    if (!fileNodeForBugCheck || !fileNodeForBugCheck.content) {
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Skipping bug checking: No code content found for ${currentTask.details}. Moving to BUGS_ANALYZED.`, 'info', 'BUG_CHECKING');
                        setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentProcessingStage: 'BUGS_ANALYZED' as TaskProcessingStage } : t) };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                        break; 
                    }
                    const codeToAnalyzeBugs = fileNodeForBugCheck.content;

                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Performing general bug check on ${currentTask.details}...`, 'working', 'BUG_CHECKING');
                    try {
                        const bugReport = await bugHunterAgent!.analyzeCodeForBugs(codeToAnalyzeBugs, currentTask.details!, currentProjectSnapshot.fullContext, currentProjectSnapshot.fileStructure, modelForCall);
                        const generalBugs = bugReport.bugs.map(b => ({ ...b, isSecurityIssue: false, attempts: 0, bugId: b.bugId || `gen-${uuidv4()}` }));

                        if (generalBugs.length > 0) {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Bug Hunter found ${generalBugs.length} general issue(s) in ${currentTask.details}.`, 'info', 'BUG_CHECKING', generalBugs.map(b => ({ title: `Bug: ${b.bugId}`, content: `Severity: ${b.severity}\nPath: ${b.filePath}\nDesc: ${b.description}${b.lineNumber ? `\nLine: ${b.lineNumber}`: ''}` })));
                            addDecisionLogEntry(agentTypeForCall, 'General Bugs Identified', `Found ${generalBugs.length} issues in ${currentTask.details}. First: ${generalBugs[0].description.substring(0,50)}...`, undefined, currentTask.id);
                        } else {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `No general bugs found in ${currentTask.details}.`, 'success', 'BUG_CHECKING');
                        }
                        
                        setProject(prev => {
                            const updatedProject = {
                                ...prev,
                                tasks: prev.tasks.map(t => {
                                    if (t.id === currentTask.id) {
                                        const existingBugs = t.identifiedBugs || [];
                                        return {
                                            ...t,
                                            identifiedBugs: [...existingBugs, ...generalBugs],
                                            currentProcessingStage: 'BUGS_ANALYZED' as TaskProcessingStage
                                        };
                                    }
                                    return t;
                                })
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });

                    } catch (error) {
                        const err = error instanceof Error ? error : new Error(String(error));
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error during bug checking for ${currentTask.details}: ${err.message}`, 'error', 'BUG_CHECKING');
                        addDecisionLogEntry(agentTypeForCall, 'Bug Checking Failed', `File: ${currentTask.details}, Error: ${err.message}`, err.stack, currentTask.id);
                        if (err instanceof RateLimitError) {
                            handleRateLimitError(agentTypeForCall, err.modelId, err.message);
                        } else {
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Bug checking failed: ${err.message}` } : t), activeTaskId: null };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    }
                    break;
                 case 'BUGS_ANALYZED':
                    addTaskLog(currentTask.id, 'Project Manager', `Analyzing consolidated bug report for ${currentTask.details}...`, 'info', 'BUGS_ANALYZED');
                    const allIdentifiedBugs = currentTask.identifiedBugs || [];
                    if (allIdentifiedBugs.length > 0) {
                        addTaskLog(currentTask.id, 'Project Manager', `${allIdentifiedBugs.length} total bug(s) identified. Moving to Refactoring.`, 'info', 'BUGS_ANALYZED', allIdentifiedBugs.map(b => ({ title: `Consolidated Bug: ${b.bugId}`, content: `Severity: ${b.severity}\nPath: ${b.filePath}\nDesc: ${b.description}${b.isSecurityIssue ? '\n(Security Issue)' : ''}` })));
                        addDecisionLogEntry('ProjectManager', 'Bugs Identified for Refactoring', `${allIdentifiedBugs.length} bugs in ${currentTask.details}. First: ${allIdentifiedBugs[0].description.substring(0,50)}...`, undefined, currentTask.id);
                        setProject(prev => {
                            const updatedProject = {
                                ...prev,
                                tasks: prev.tasks.map(t => t.id === currentTask.id ? {
                                    ...t,
                                    unresolvedBugs: [...allIdentifiedBugs], 
                                    identifiedBugs: [], 
                                    currentProcessingStage: 'REFACTORING' as TaskProcessingStage,
                                    bugFixingCycles: (t.bugFixingCycles || 0) + 1 
                                } : t)
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                    } else {
                        addTaskLog(currentTask.id, 'Project Manager', `No bugs identified in ${currentTask.details} after all checks. Proceeding to context update.`, 'success', 'BUGS_ANALYZED');
                        setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentProcessingStage: 'CONTEXT_UPDATING' as TaskProcessingStage, unresolvedBugs: [], identifiedBugs: [] } : t)};
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                    }
                    break;
                case 'REFACTORING':
                    agentTypeForCall = AgentType.REFACTORER;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    
                    if (currentTask.bugFixingCycles >= MAX_BUG_FIXING_CYCLES_PER_TASK) {
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Task exceeded maximum bug fixing cycles (${MAX_BUG_FIXING_CYCLES_PER_TASK}). Failing task.`, 'error', 'REFACTORING');
                        setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Exceeded ${MAX_BUG_FIXING_CYCLES_PER_TASK} bug fixing cycles.` } : t), activeTaskId: null };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                        break;
                    }

                    const bugToFix = currentTask.unresolvedBugs && currentTask.unresolvedBugs.length > 0 ? currentTask.unresolvedBugs[0] : null;

                    if (!bugToFix) {
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), 'No unresolved bugs to refactor. Moving to context update.', 'info', 'REFACTORING');
                        setProject(prev => {
                            const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentProcessingStage: 'CONTEXT_UPDATING' as TaskProcessingStage, currentRefactoringBugId: undefined } : t)};
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                        break;
                    }
                     setProject(prev => { 
                        const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, currentRefactoringBugId: bugToFix.bugId } : t)};
                        // Do not save here, this is an intermediate state for UI display
                        return updatedProject; 
                     });


                    if (bugToFix.attempts >= MAX_ATTEMPTS_PER_BUG) {
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Bug "${bugToFix.description.substring(0,50)}..." (ID: ${bugToFix.bugId}) in ${bugToFix.filePath} exceeded max attempts (${MAX_ATTEMPTS_PER_BUG}). Skipping.`, 'error', 'REFACTORING');
                        setProject(prev => {
                             const updatedProject = {
                                ...prev,
                                tasks: prev.tasks.map(t => t.id === currentTask.id ? {
                                    ...t,
                                    unresolvedBugs: t.unresolvedBugs.slice(1), 
                                } : t)
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                        break; 
                    }

                    const fileToRefactorNode = findFileNodeByPath(currentProjectSnapshot.fileStructure, bugToFix.filePath);
                    if (!fileToRefactorNode || fileToRefactorNode.type === 'folder' || typeof fileToRefactorNode.content !== 'string') {
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Cannot refactor: File content for "${bugToFix.filePath}" not found. Marking bug as unfixable for now.`, 'error', 'REFACTORING');
                        setProject(prev => {
                             const updatedProject = {
                                ...prev,
                                tasks: prev.tasks.map(t => t.id === currentTask.id ? {
                                    ...t,
                                    unresolvedBugs: t.unresolvedBugs.map(b => b.bugId === bugToFix.bugId ? {...b, attempts: MAX_ATTEMPTS_PER_BUG } : b).slice(1) 
                                } : t)
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });
                        break;
                    }

                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Attempting to refactor bug (Attempt ${bugToFix.attempts + 1}/${MAX_ATTEMPTS_PER_BUG}): "${bugToFix.description.substring(0,50)}..." in ${bugToFix.filePath}`, 'working', 'REFACTORING');
                    try {
                        const refactorResponse = await refactorerAgent!.refactorCode(
                            fileToRefactorNode.content, bugToFix.description, bugToFix.bugId, bugToFix.filePath, currentProjectSnapshot.fullContext, currentProjectSnapshot.fileStructure, modelForCall, bugToFix.isSecurityIssue
                        );

                        if (refactorResponse.clarificationQuestion) {
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Refactorer needs clarification: ${refactorResponse.clarificationQuestion}`, 'info', 'CLARIFICATION_NEEDED');
                            setProject(prev => {
                                const updatedProject = { ...prev, tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, clarificationQuestion: refactorResponse.clarificationQuestion, originalStageBeforeClarification: 'REFACTORING' as TaskProcessingStage, currentProcessingStage: 'CLARIFICATION_NEEDED' as TaskProcessingStage, status: 'pending' as TaskStatus } : t)};
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        } else {
                            let updatedFileStructure = [...currentProjectSnapshot.fileStructure]; // Use snapshot here
                            refactorResponse.fileChanges.forEach(change => {
                                updatedFileStructure = addOrUpdateFileNodeImmutable(updatedFileStructure, change.filePath, change.fixedCode);
                            });
                            
                            const explanation = refactorResponse.explanation || "Bug fix applied.";
                            addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Refactoring attempt for bug ${bugToFix.bugId} successful. Explanation: ${explanation.substring(0,100)}...`, 'success', 'REFACTORING', [{title: "Refactoring Details", content: explanation}]);
                            addDecisionLogEntry(agentTypeForCall, 'Code Refactored', `Bug ID: ${bugToFix.bugId}, File: ${bugToFix.filePath}. Fix: ${explanation.substring(0,100)}...`, undefined, currentTask.id);

                            let newArchitecturalSuggestions = currentProjectSnapshot.architecturalSuggestions;
                            if (explanation.includes("Architectural Suggestion:")) {
                                const suggestion = explanation.substring(explanation.indexOf("Architectural Suggestion:") + "Architectural Suggestion:".length).trim();
                                if (suggestion && !newArchitecturalSuggestions.includes(suggestion)) {
                                    newArchitecturalSuggestions = [...newArchitecturalSuggestions, suggestion];
                                    addCompanyLog(getAgentDisplayName(agentTypeForCall), `New architectural suggestion: ${suggestion}`, 'info', currentTask.id);
                                }
                            }
                            
                            setProject(prev => {
                                const updatedProject = {
                                    ...prev,
                                    fileStructure: updatedFileStructure, // Apply the changes from refactoring
                                    tasks: prev.tasks.map(t => t.id === currentTask.id ? {
                                        ...t,
                                        code: t.details === bugToFix.filePath ? refactorResponse.fileChanges.find(fc => fc.filePath === bugToFix.filePath)?.fixedCode || t.code : t.code,
                                        unresolvedBugs: t.unresolvedBugs.filter(b => b.bugId !== bugToFix.bugId), 
                                        currentProcessingStage: 'LINTING_FORMATTING' as TaskProcessingStage, 
                                        clarifierResponse: undefined,
                                        currentRefactoringBugId: undefined,
                                    } : t),
                                    currentFilePreview: findFileNodeByPath(updatedFileStructure, bugToFix.filePath) || prev.currentFilePreview,
                                    architecturalSuggestions: newArchitecturalSuggestions
                                };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    } catch (err) {
                        const errorMsg = err instanceof Error ? err.message : String(err);
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error refactoring bug ${bugToFix.bugId} in ${bugToFix.filePath}: ${errorMsg}`, 'error', 'REFACTORING');
                        addDecisionLogEntry(agentTypeForCall, 'Refactoring Failed', `Bug ID: ${bugToFix.bugId}, File: ${bugToFix.filePath}, Error: ${errorMsg}`, err instanceof Error ? err.stack : undefined, currentTask.id);

                        if (err instanceof RateLimitError) {
                            handleRateLimitError(agentTypeForCall, err.modelId, errorMsg);
                        } else {
                            setProject(prev => {
                                const updatedProject = {
                                    ...prev,
                                    tasks: prev.tasks.map(t => t.id === currentTask.id ? {
                                        ...t,
                                        unresolvedBugs: t.unresolvedBugs.map(b => b.bugId === bugToFix.bugId ? { ...b, attempts: b.attempts + 1 } : b),
                                        currentRefactoringBugId: undefined, 
                                    } : t),
                                };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    }
                    break;
                 case 'CONTEXT_UPDATING':
                    agentTypeForCall = AgentType.CONTEXT_MANAGER;
                    modelForCall = currentProjectSnapshot.agentModelConfiguration[agentTypeForCall];
                    addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Updating project context after work on '${currentTask.details || currentTask.description}'...`, 'working', 'CONTEXT_UPDATING');
                    try {
                        const newInfo = `Task (ID: ${currentTask.id}) "${currentTask.description}" for file "${currentTask.details}" is now considered complete and all checks passed. Code content for this file is finalized for this iteration.`;
                        const contextUpdateResponse = await contextManagerAgent!.updateProjectContext(currentProjectSnapshot.fullContext, newInfo, modelForCall);
                        
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Project context updated successfully after task for ${currentTask.details}.`, 'success', 'CONTEXT_UPDATED');
                        addDecisionLogEntry(agentTypeForCall, 'Project Context Updated', `Context updated after completion of task: ${currentTask.description}`, undefined, currentTask.id);
                        
                        setProject(prev => {
                            const updatedProject = {
                                ...prev,
                                fullContext: contextUpdateResponse.updatedContext,
                                tasks: prev.tasks.map(t => t.id === currentTask.id ? {
                                    ...t,
                                    status: 'completed' as TaskStatus,
                                    currentProcessingStage: 'DONE' as TaskProcessingStage,
                                    error: undefined,
                                    unresolvedBugs: [],
                                    currentRefactoringBugId: undefined,
                                } : t),
                                activeTaskId: null,
                            };
                            if (prev.id) handleSaveCurrentProject(updatedProject);
                            return updatedProject;
                        });

                    } catch (err) {
                        const errorMsg = err instanceof Error ? err.message : String(err);
                        addTaskLog(currentTask.id, getAgentDisplayName(agentTypeForCall), `Error updating project context for ${currentTask.details}: ${errorMsg}`, 'error', 'CONTEXT_UPDATING');
                        addDecisionLogEntry(agentTypeForCall, 'Context Update Failed', `Task: ${currentTask.description}, File: ${currentTask.details}, Error: ${errorMsg}`, err instanceof Error ? err.stack : undefined, currentTask.id);
                        if (err instanceof RateLimitError) {
                            handleRateLimitError(agentTypeForCall, err.modelId, errorMsg);
                        } else {
                            setProject(prev => {
                                const updatedProject = {
                                    ...prev,
                                    tasks: prev.tasks.map(t => t.id === currentTask.id ? { ...t, status: 'error' as TaskStatus, error: `Context update failed: ${errorMsg}` } : t),
                                    activeTaskId: null
                                };
                                if (prev.id) handleSaveCurrentProject(updatedProject);
                                return updatedProject;
                            });
                        }
                    }
                    break;
            } // End switch (currentTask.currentProcessingStage)
            break; // End case ProjectLifecyclePhase.AUTONOMOUS_EXECUTION
            
            case ProjectLifecyclePhase.TESTING_PLANNING:
                const testerAgentForPlan = AgentType.TESTER;
                const testerModelForPlan = currentProjectSnapshot.agentModelConfiguration[testerAgentForPlan];
                addCompanyLog(getAgentDisplayName(testerAgentForPlan), `Generating test plan using ${currentProjectSnapshot.availableModels.find(m=>m.id === testerModelForPlan)?.name || testerModelForPlan}...`, 'working');
                try {
                    const testPlanResponse = await testerAgent!.generateTestPlan(currentProjectSnapshot.fullContext, currentProjectSnapshot.fileStructure, testerModelForPlan);
                    const newTestTasks: Task[] = testPlanResponse.testFiles.map(tf => ({
                        id: uuidv4(),
                        description: `Generate test code: ${tf.description}`,
                        details: tf.filePath,
                        purpose: 'generate-test-code', // Special purpose for Coder
                        relatedSourceFiles: tf.relatedSourceFiles,
                        status: 'pending',
                        currentProcessingStage: 'QUEUED',
                        agentMessages: [], identifiedBugs: [], unresolvedBugs: [], bugFixingCycles: 0,
                        priority: 'medium', // Default priority for test generation
                        estimatedComplexity: 'medium', // Default complexity
                    }));

                    addCompanyLog(getAgentDisplayName(testerAgentForPlan), `Test plan generated. ${newTestTasks.length} test file(s) to create. Strategy: ${testPlanResponse.overallStrategy || 'N/A'}`, 'success');
                    addDecisionLogEntry(testerAgentForPlan, 'Test Plan Generated', `Generated ${newTestTasks.length} test file tasks. Overall Strategy: ${testPlanResponse.overallStrategy || 'Default behavior-driven tests.'}`, undefined, currentProjectSnapshot.id);
                    
                    setProject(prev => {
                        const updatedState = {
                            ...prev,
                            tasks: [...prev.tasks, ...newTestTasks],
                            projectLifecycle: ProjectLifecyclePhase.AUTONOMOUS_EXECUTION, // Go back to execute these tasks
                            activeTaskId: null,
                            fullContext: prev.fullContext + `\nTest Plan Summary: ${testPlanResponse.overallStrategy || 'Generated test files based on source code analysis.'} ${newTestTasks.length} new test files planned.`
                        };
                        if (prev.id) handleSaveCurrentProject(updatedState);
                        return updatedState;
                    });

                } catch (error) {
                    const err = error instanceof Error ? error : new Error(String(error));
                    addCompanyLog(getAgentDisplayName(testerAgentForPlan), `Error generating test plan: ${err.message}`, 'error');
                    addDecisionLogEntry(testerAgentForPlan, 'Test Plan Generation Failed', `Error: ${err.message}`, err.stack, currentProjectSnapshot.id);
                    if (err instanceof RateLimitError) handleRateLimitError(testerAgentForPlan, err.modelId, err.message);
                    else {
                       setProject(prev => {
                         const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Test planning failed: ${err.message}` };
                         if (prev.id) handleSaveCurrentProject(errorState);
                         return errorState;
                       });
                    }
                }
                break;

            case ProjectLifecyclePhase.TEST_ANALYSIS_PENDING:
                const testerAgentForAnalysis = AgentType.TESTER;
                const testerModelForAnalysis = currentProjectSnapshot.agentModelConfiguration[testerAgentForAnalysis];
                addCompanyLog(getAgentDisplayName(testerAgentForAnalysis), `Running tests and analyzing results using ${currentProjectSnapshot.availableModels.find(m=>m.id === testerModelForAnalysis)?.name || testerModelForAnalysis}...`, 'working');
                try {
                    const testAnalysisResponse = await testerAgent!.analyzeProjectWithTests(
                        currentProjectSnapshot.fullContext,
                        currentProjectSnapshot.fileStructure,
                        testerModelForAnalysis
                    );
                    
                    addCompanyLog(getAgentDisplayName(testerAgentForAnalysis), `Test analysis complete. ${testAnalysisResponse.issues.length} issue(s) found from test failures. Summary:\n${testAnalysisResponse.summary}`, 'info');
                    addDecisionLogEntry(testerAgentForAnalysis, 'Test Analysis Completed', `Found ${testAnalysisResponse.issues.length} issues from test runs. Summary: ${testAnalysisResponse.summary?.substring(0,100)}...`, undefined, currentProjectSnapshot.id);

                    if (testAnalysisResponse.issues.length > 0) {
                        const newBugTasks: Task[] = [];
                        for (const issue of testAnalysisResponse.issues) {
                            // Analyze each test failure to pinpoint source code bug
                            let sourceBugInfo: BugInfo = { ...issue, bugId: `test-analysis-refined-${uuidv4().substring(0,8)}` }; 
                            try {
                                const testFileNode = findFileNodeByPath(currentProjectSnapshot.fileStructure, issue.originalTestFailure!.testFilePath);
                                if (testFileNode && testFileNode.content) {
                                     const singleFailureAnalysis = await testerAgent!.analyzeSingleTestFailure(issue, testFileNode, currentProjectSnapshot.fileStructure, currentProjectSnapshot.fullContext, testerModelForAnalysis);
                                     sourceBugInfo = singleFailureAnalysis.analyzedBugInfo;
                                     addTaskLog("system-test-analysis", getAgentDisplayName(testerAgentForAnalysis), `Refined bug for test failure "${issue.originalTestFailure?.testName}": ${sourceBugInfo.description}`, 'info');
                                } else {
                                     addTaskLog("system-test-analysis", getAgentDisplayName(testerAgentForAnalysis), `Could not load test file content for "${issue.originalTestFailure?.testFilePath}" to refine bug. Using original test failure info.`, 'info');
                                }
                            } catch (refineError) {
                                const rError = refineError instanceof Error ? refineError.message : String(refineError);
                                addTaskLog("system-test-analysis", getAgentDisplayName(testerAgentForAnalysis), `Error refining bug from test failure "${issue.originalTestFailure?.testName}": ${rError}. Using original test failure info.`, 'error');
                            }

                            newBugTasks.push({
                                id: uuidv4(),
                                description: `Fix test failure: ${sourceBugInfo.description.substring(0,100)}... (Original test: ${issue.originalTestFailure?.testName || 'Unknown'})`,
                                details: sourceBugInfo.filePath, // File path from the BugInfo (source file)
                                purpose: 'fix-test-failure',
                                status: 'pending',
                                currentProcessingStage: 'QUEUED',
                                agentMessages: [],
                                identifiedBugs: [sourceBugInfo], // The bug from test analysis
                                unresolvedBugs: [],
                                bugFixingCycles: 0,
                                priority: 'high',
                                estimatedComplexity: 'medium',
                            });
                        }

                        setProject(prev => {
                            const updatedState = {
                                ...prev,
                                tasks: [...prev.tasks, ...newBugTasks],
                                projectLifecycle: ProjectLifecyclePhase.AUTONOMOUS_EXECUTION, // Go fix these bugs
                                activeTaskId: null,
                                testingCycleCount: prev.testingCycleCount + 1,
                                fullContext: prev.fullContext + `\nTest Analysis: ${testAnalysisResponse.issues.length} failures. Created ${newBugTasks.length} tasks to address them. Cycle: ${prev.testingCycleCount + 1}.`
                            };
                            if (prev.id) handleSaveCurrentProject(updatedState);
                            return updatedState;
                        });
                    } else { // No issues from test runs
                        if (currentProjectSnapshot.testingCycleCount < 1) { // First successful pass, check coverage
                            addCompanyLog(getAgentDisplayName(testerAgentForAnalysis), 'All tests passed. Proceeding to test coverage analysis.', 'success');
                            setProject(prev => {
                                const updatedState = { ...prev, projectLifecycle: ProjectLifecyclePhase.AWAITING_TEST_COVERAGE_ANALYSIS };
                                if (prev.id) handleSaveCurrentProject(updatedState);
                                return updatedState;
                            });
                        } else { // Subsequent pass and all tests passed
                            addCompanyLog(getAgentDisplayName(testerAgentForAnalysis), 'All tests passed after bug fixes. Project verified.', 'success');
                            setProject(prev => {
                                const updatedState = { ...prev, projectLifecycle: ProjectLifecyclePhase.PROJECT_VERIFIED };
                                if (prev.id) handleSaveCurrentProject(updatedState);
                                return updatedState;
                            });
                        }
                    }

                } catch (error) {
                    const err = error instanceof Error ? error : new Error(String(error));
                    addCompanyLog(getAgentDisplayName(testerAgentForAnalysis), `Error during test analysis: ${err.message}`, 'error');
                    addDecisionLogEntry(testerAgentForAnalysis, 'Test Analysis Failed', `Error: ${err.message}`, err.stack, currentProjectSnapshot.id);
                    if (err instanceof RateLimitError) handleRateLimitError(testerAgentForAnalysis, err.modelId, err.message);
                    else {
                       setProject(prev => {
                         const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Test analysis critically failed: ${err.message}` };
                         if (prev.id) handleSaveCurrentProject(errorState);
                         return errorState;
                       });
                    }
                }
                break;
            
            case ProjectLifecyclePhase.AWAITING_TEST_COVERAGE_ANALYSIS:
                const testerAgentForCoverage = AgentType.TESTER;
                const testerModelForCoverage = currentProjectSnapshot.agentModelConfiguration[testerAgentForCoverage];
                addCompanyLog(getAgentDisplayName(testerAgentForCoverage), `Analyzing test coverage using ${currentProjectSnapshot.availableModels.find(m=>m.id === testerModelForCoverage)?.name || testerModelForCoverage}...`, 'working');
                try {
                    const coverageResponse = await testerAgent!.analyzeTestCoverage(
                        currentProjectSnapshot.fullContext,
                        currentProjectSnapshot.fileStructure,
                        testerModelForCoverage
                    );
                    
                    if (coverageResponse.testFiles.length > 0) {
                        const newCoverageTasks: Task[] = coverageResponse.testFiles.map(tf => ({
                            id: uuidv4(),
                            description: `Improve test coverage: ${tf.description}`,
                            details: tf.filePath,
                            purpose: 'generate-test-code',
                            relatedSourceFiles: tf.relatedSourceFiles,
                            status: 'pending',
                            currentProcessingStage: 'QUEUED',
                            agentMessages: [], identifiedBugs: [], unresolvedBugs: [], bugFixingCycles: 0,
                            priority: 'medium',
                            estimatedComplexity: 'medium',
                        }));
                        addCompanyLog(getAgentDisplayName(testerAgentForCoverage), `Test coverage analysis suggests ${newCoverageTasks.length} new test file(s) to improve coverage.`, 'info');
                        addDecisionLogEntry(testerAgentForCoverage, 'Test Coverage Gaps Identified', `Identified ${newCoverageTasks.length} areas for test coverage improvement.`, undefined, currentProjectSnapshot.id);
                        setProject(prev => {
                            const updatedState = {
                                ...prev,
                                tasks: [...prev.tasks, ...newCoverageTasks],
                                projectLifecycle: ProjectLifecyclePhase.AUTONOMOUS_EXECUTION,
                                activeTaskId: null,
                                fullContext: prev.fullContext + `\nTest Coverage Analysis: Suggested ${newCoverageTasks.length} new test files.`
                            };
                            if (prev.id) handleSaveCurrentProject(updatedState);
                            return updatedState;
                        });
                    } else {
                        addCompanyLog(getAgentDisplayName(testerAgentForCoverage), 'Test coverage analysis complete. Coverage deemed sufficient. Project verified.', 'success');
                         setProject(prev => {
                            const updatedState = { ...prev, projectLifecycle: ProjectLifecyclePhase.PROJECT_VERIFIED };
                            if (prev.id) handleSaveCurrentProject(updatedState);
                            return updatedState;
                        });
                    }
                } catch (error) {
                    const err = error instanceof Error ? error : new Error(String(error));
                    addCompanyLog(getAgentDisplayName(testerAgentForCoverage), `Error during test coverage analysis: ${err.message}`, 'error');
                    addDecisionLogEntry(testerAgentForCoverage, 'Test Coverage Analysis Failed', `Error: ${err.message}`, err.stack, currentProjectSnapshot.id);
                    if (err instanceof RateLimitError) handleRateLimitError(testerAgentForCoverage, err.modelId, err.message);
                    else {
                       setProject(prev => {
                         const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Test coverage analysis failed: ${err.message}` };
                         if (prev.id) handleSaveCurrentProject(errorState);
                         return errorState;
                       });
                    }
                }
                break;

          case ProjectLifecyclePhase.PROJECT_BUILD_VALIDATION:
                const agentTypeForBuild = AgentType.PLANNER; 
                const modelForBuild = currentProjectSnapshot.agentModelConfiguration[agentTypeForBuild];
                addCompanyLog(getAgentDisplayName(agentTypeForBuild), `Performing conceptual build validation using ${currentProjectSnapshot.availableModels.find(m=>m.id === modelForBuild)?.name || modelForBuild}...`, 'working');
                try {
                    const packageJsonNode = findFileNodeByPath(currentProjectSnapshot.fileStructure, 'package.json');
                    const buildValidationResponse = await (new PlannerAgent(geminiService!)).validateProjectBuild(currentProjectSnapshot.fullContext, currentProjectSnapshot.fileStructure, packageJsonNode?.content, modelForBuild);
                    
                    addDecisionLogEntry(agentTypeForBuild, 'Build Validation Attempted', `Type: ${buildValidationResponse.projectType || 'N/A'}. Command: ${buildValidationResponse.buildCommand || 'N/A'}. Issues: ${buildValidationResponse.buildIssues.length}. Summary: ${buildValidationResponse.validationSummary}`, undefined, currentProjectSnapshot.id);

                    if (buildValidationResponse.buildIssues.length > 0) {
                        addCompanyLog(getAgentDisplayName(agentTypeForBuild), `Build validation found ${buildValidationResponse.buildIssues.length} issue(s). Summary: ${buildValidationResponse.validationSummary}`, 'error', undefined);
                        setProject(prev => {
                            const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Build validation failed: ${buildValidationResponse.buildIssues[0].description.substring(0,100)}... See logs for details. (${buildValidationResponse.validationSummary})` };
                            if (prev.id) handleSaveCurrentProject(errorState);
                            return errorState;
                        });
                    } else {
                        addCompanyLog(getAgentDisplayName(agentTypeForBuild), `Conceptual build validation successful. ${buildValidationResponse.validationSummary}`, 'success');
                        setProject(prev => {
                            const successState = { ...prev, projectLifecycle: ProjectLifecyclePhase.PROJECT_READY_FOR_DEPLOYMENT };
                            if (prev.id) handleSaveCurrentProject(successState);
                            return successState;
                        });
                    }
                } catch (error) {
                    const err = error instanceof Error ? error : new Error(String(error));
                    addCompanyLog(getAgentDisplayName(agentTypeForBuild), `Error during build validation: ${err.message}`, 'error');
                    if (err instanceof RateLimitError) handleRateLimitError(agentTypeForBuild, err.modelId, err.message);
                    else {
                        setProject(prev => {
                           const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Build validation critically failed: ${err.message}` };
                           if (prev.id) handleSaveCurrentProject(errorState);
                           return errorState;
                        });
                    }
                }
                break;
        } 

      } catch (error) { 
        const err = error instanceof Error ? error : new Error(String(error));
        addCompanyLog('System', `Critical error in autonomous execution: ${err.message}`, 'error');
        addDecisionLogEntry('System', 'Execution Error', `Critical failure: ${err.message}. Lifecycle: ${project.projectLifecycle}`, err.stack); // Use original project for logging context
        setProject(prev => {
          const errorState = { ...prev, projectLifecycle: ProjectLifecyclePhase.EXECUTION_HALTED_ERROR, errorMessage: `Execution error: ${err.message}` };
          if (prev.id) handleSaveCurrentProject(errorState); 
          return errorState;
        });
      } finally {
           autonomousExecutionRunningRef.current = false;
      }
    }; 

    const isTaskProcessingPhase = project.projectLifecycle === ProjectLifecyclePhase.AUTONOMOUS_EXECUTION && project.tasks.some(t => t.status === 'pending' || t.status === 'in-progress');
    const isOtherAutomatedPhase = validExecutionPhases.includes(project.projectLifecycle) && project.projectLifecycle !== ProjectLifecyclePhase.AUTONOMOUS_EXECUTION;


    const shouldRun = 
      project.id && 
      project.apiKey && 
      geminiService &&
      clarifierAgent &&
      (isTaskProcessingPhase || isOtherAutomatedPhase) &&
      project.currentPhase === OperatingPhase.COMPANY_OPERATIONAL &&
      !project.rateLimitInfo; 

    if (shouldRun) {
        processNextStep();
    } else {
       autonomousExecutionRunningRef.current = false; 
       if (project.rateLimitInfo && project.projectLifecycle !== ProjectLifecyclePhase.AWAITING_RATE_LIMIT_RESOLUTION) {
            console.warn("Rate limit info present but lifecycle not AWAITING_RATE_LIMIT_RESOLUTION. Potential state inconsistency.");
       }
    }
  }, [
    project, 
    geminiService, 
    clarifierAgent,
    addCompanyLog, 
    addTaskLog, 
    handleRateLimitError,
    addDecisionLogEntry,
    getAgentTypeForStage,
    handleSaveCurrentProject, 
  ]);


  const renderCurrentPhaseContent = () => {
    switch (project.currentPhase) {
      case OperatingPhase.AGENT_MODEL_CONFIGURATION:
        return <AgentModelConfigurationModal models={project.availableModels} currentConfig={project.agentModelConfiguration} onSubmit={handleAgentModelConfigSubmit} />;
      case OperatingPhase.API_KEY_INPUT:
        return <ApiKeyModal onSubmit={handleApiKeySubmit} />;
      case OperatingPhase.COMPANY_OPERATIONAL:
        if (project.projectLifecycle === ProjectLifecyclePhase.AWAITING_PROJECT_IDEA || project.projectLifecycle === ProjectLifecyclePhase.AWAITING_LICENSE_CHOICE) {
          return null;
        }
        return null; 
      default:
        return <p>Unknown operating phase.</p>;
    }
  };
  
  const handleFileSelect = (file: FileNode) => {
    setProject(prev => ({ ...prev, currentFilePreview: file }));
  };
  
  const handleExportProject = () => {
    const zipFileName = sanitizeFilename(project.name || project.idea || "ai_project");
    const zip = new JSZip();
    const projectFolder = zip.folder(zipFileName);

    if (!projectFolder) {
        addCompanyLog('System', 'Failed to create project folder for ZIP export.', 'error');
        return;
    }

    const addFilesToZip = (nodes: FileNode[], currentFolder: JSZip) => {
      nodes.forEach(node => {
        if (node.type === 'file') {
          let contentToZip = node.content || '';
          if (contentToZip === IMAGE_DATA_PLACEHOLDER_FOR_STORAGE) {
            const liveFileNode = findFileNodeByPath(project.fileStructure, node.path);
            if (liveFileNode && liveFileNode.content && liveFileNode.content !== IMAGE_DATA_PLACEHOLDER_FOR_STORAGE) {
                contentToZip = liveFileNode.content;
            }
          }
          currentFolder.file(node.name, contentToZip, { binary: contentToZip?.startsWith('data:image/') });
        } else if (node.type === 'folder') {
          const folder = currentFolder.folder(node.name);
          if (folder && node.children) {
            addFilesToZip(node.children, folder);
          }
        }
      });
    };

    addFilesToZip(project.fileStructure, projectFolder);

    const projectDataToSummarize = {...project}; 
    projectDataToSummarize.tasks = projectDataToSummarize.tasks.map(t => ({
        ...t, 
        agentMessages: t.agentMessages.slice(-MAX_TASK_AGENT_MESSAGES_IN_CONTEXT) // Use context limit
    }));
    projectDataToSummarize.companyLogs = projectDataToSummarize.companyLogs.slice(-MAX_COMPANY_LOGS_IN_CONTEXT); // Use context limit


    const projectSummary = {
        id: projectDataToSummarize.id,
        name: projectDataToSummarize.name,
        idea: projectDataToSummarize.idea,
        lastModified: projectDataToSummarize.lastModified,
        fullContext: projectDataToSummarize.fullContext,
        tasks: projectDataToSummarize.tasks.map(t => ({
            id: t.id, description: t.description, status: t.status, details: t.details,
            error: t.error, agentMessagesCount: t.agentMessages.length,
            priority: t.priority, dependencies: t.dependencies, estimatedComplexity: t.estimatedComplexity
        })), 
        companyLogsCount: projectDataToSummarize.companyLogs.length, 
        decisionLog: projectDataToSummarize.decisionLog,
        architecturalNotes: projectDataToSummarize.architecturalNotes,
        devNotes: projectDataToSummarize.devNotes,
        suggestedTechnologyStack: projectDataToSummarize.suggestedTechnologyStack,
        licenseInfo: projectDataToSummarize.licenseInfo, 
        finalFileStructureOverview: projectDataToSummarize.fileStructure.map(f => ({name: f.name, path: f.path, type: f.type, isTestFile: f.isTestFile, hasContent: !!f.content && f.content !== IMAGE_DATA_PLACEHOLDER_FOR_STORAGE}))
    };
    projectFolder.file('_PROJECT_SUMMARY.json', JSON.stringify(projectSummary, null, 2));


    zip.generateAsync({ type: "blob" })
      .then(function(content) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(content);
        link.download = `${zipFileName}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
        addCompanyLog('System', 'Project files exported as ZIP.', 'success');
        addDecisionLogEntry('User', 'Project Exported', 'User initiated project download.');
      })
      .catch(err => {
        addCompanyLog('System', `Error exporting project: ${err.message}`, 'error');
        console.error("Error generating ZIP:", err);
      });
  };

  const handleUserFeedbackSubmit = async (feedback: UserFeedback) => {
    setIsFeedbackLoading(true);
    addCompanyLog('User', `Feedback submitted: ${feedback.type} - ${feedback.description.substring(0, 50)}...`, 'info');
    addDecisionLogEntry('User', 'User Feedback Submitted', `Type: ${feedback.type}, Path: ${feedback.filePath || 'N/A'}, Desc: ${feedback.description.substring(0,100)}...`);

    setProject(prev => {
      const updatedState = {
        ...prev,
        lastUserFeedback: feedback,
        projectLifecycle: ProjectLifecyclePhase.PLANNING_USER_FEEDBACK,
        activeTaskId: null, 
      };
      if (prev.id) handleSaveCurrentProject(updatedState);
      return updatedState;
    });
    setIsFeedbackLoading(false);
  };
  
  const completedTasks = project.tasks.filter(t => t.status === 'completed').length;
  const totalTasks = project.tasks.length;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  let activeAgentAndModelString: string | null = null;
  if (project.activeTaskId) {
    const currentActiveTask = project.tasks.find(t => t.id === project.activeTaskId);
    if (currentActiveTask && currentActiveTask.status === 'in-progress') {
      const agentType = getAgentTypeForStage(currentActiveTask.currentProcessingStage);
      if (agentType && agentType !== 'ProjectManager') { 
        const modelId = project.agentModelConfiguration[agentType as AgentType];
        const modelDetails = project.availableModels.find(m => m.id === modelId);
        const modelName = modelDetails ? modelDetails.name.substring(0,15) + "..." : modelId;
        activeAgentAndModelString = `${getAgentDisplayName(agentType)} (${modelName})`;
      } else if (agentType === 'ProjectManager') {
        activeAgentAndModelString = `${getAgentDisplayName(agentType)} orchestrating`;
      }
    }
  }


  if (project.currentPhase !== OperatingPhase.COMPANY_OPERATIONAL) {
    const content = renderCurrentPhaseContent();
    if (!content) return null; 

    return (
      <div className="app-container bg-gray-900 text-gray-100 min-h-screen flex flex-col items-center justify-center p-4">
        {content}
        {project.errorMessage && (
          <p className="mt-4 text-red-400 bg-red-800 p-3 rounded-md text-sm shadow-lg">Error: {project.errorMessage}</p>
        )}
      </div>
    );
  }
  
  if (project.projectLifecycle === ProjectLifecyclePhase.AWAITING_PROJECT_IDEA && !project.id) {
    return (
        <div className="app-container bg-gray-900 text-gray-100 min-h-screen flex flex-col p-4 items-center">
            <header className="w-full max-w-4xl mx-auto py-6 text-center">
                 <h1 className="text-3xl sm:text-4xl font-bold text-purple-400 mb-2">
                    DevGenius Studio <span className="text-sm text-purple-600">v0.9.3-backend</span>
                </h1>
                <p className="text-gray-400">Load an existing project from the backend or start a new one.</p>
            </header>
            <main className="w-full flex-grow flex flex-col items-center justify-center">
                <div className="w-full max-w-4xl space-y-12">
                    <SavedProjectsList 
                        projects={savedProjects} 
                        onLoadProject={handleLoadProject} 
                        onDeleteProject={(id) => setShowDeleteConfirmModal(id)} 
                    />
                    <ProjectInputForm onSubmit={handleCreateNewProject} isLoading={isProjectIdeaSubmitting} />
                </div>
            </main>
            {showDeleteConfirmModal && savedProjects.find(p=>p.id === showDeleteConfirmModal) && (
                 <ConfirmModal 
                    title="Delete Project"
                    message={`Are you sure you want to permanently delete the project "${savedProjects.find(p=>p.id === showDeleteConfirmModal)?.name || 'this project'}"? This action cannot be undone.`}
                    onConfirm={() => handleDeleteProject(showDeleteConfirmModal)}
                    onCancel={() => setShowDeleteConfirmModal(null)}
                    confirmText="Delete Project"
                />
            )}
        </div>
    );
}

  if (showLicenseChoiceModal) {
    return (
      <LicenseChoiceModal 
        isOpen={showLicenseChoiceModal}
        onClose={() => setShowLicenseChoiceModal(false)} 
        onSubmit={handleLicenseChoiceSubmit}
        hasExistingAuthorship={!!userAuthorshipDetails}
      />
    );
  }

  if (showAuthorshipFormModal) {
    return (
      <AuthorshipFormModal 
        isOpen={showAuthorshipFormModal}
        onClose={() => {
          setShowAuthorshipFormModal(false);
          // If user cancels authorship, and we were in AWAITING_LICENSE_CHOICE, reset a bit
          if (project.projectLifecycle === ProjectLifecyclePhase.AWAITING_LICENSE_CHOICE) {
             setProject(prev => ({...prev, projectLifecycle: ProjectLifecyclePhase.AWAITING_PROJECT_IDEA, id: '' })); // Go back to project idea screen
          }
        }}
        onSubmit={handleAuthorshipFormSubmit}
      />
    );
  }


  return (
    <div className="app-container bg-gray-900 text-gray-100 min-h-screen flex flex-col relative">
      {project.projectLifecycle === ProjectLifecyclePhase.AWAITING_RATE_LIMIT_RESOLUTION && project.rateLimitInfo && (
        <RateLimitModal 
          rateLimitInfo={project.rateLimitInfo}
          availableModels={project.availableModels}
          onSwitchModel={(newModelId) => {
            if (project.rateLimitInfo) {
              const agentType = project.rateLimitInfo.agentType;
              const oldModelId = project.rateLimitInfo.modelId;
              setProject(prev => {
                const updatedConfig = { ...prev.agentModelConfiguration, [agentType]: newModelId };
                localStorage.setItem('agentModelConfiguration', JSON.stringify(updatedConfig)); 
                const updatedState = {
                  ...prev,
                  agentModelConfiguration: updatedConfig,
                  projectLifecycle: prev.id ? ProjectLifecyclePhase.PROJECT_PAUSED_ON_RATE_LIMIT : ProjectLifecyclePhase.IDLE, 
                  rateLimitInfo: undefined, 
                  errorMessage: undefined,
                };
                addCompanyLog('System', `User switched model for ${getAgentDisplayName(agentType)} from ${prev.availableModels.find(m=>m.id === oldModelId)?.name || oldModelId} to ${prev.availableModels.find(m=>m.id === newModelId)?.name || newModelId} due to rate limit. Project now paused.`, 'info');
                addDecisionLogEntry('User', 'Model Switched (Rate Limit)', `Agent: ${agentType}, Old Model: ${oldModelId}, New Model: ${newModelId}. Project paused.`);
                if (prev.id) handleSaveCurrentProject(updatedState);
                return updatedState;
              });
            }
          }}
          onPauseProject={() => {
            setProject(prev => {
              const updatedState = {
                 ...prev, 
                 projectLifecycle: ProjectLifecyclePhase.PROJECT_PAUSED_ON_RATE_LIMIT,
                 errorMessage: undefined 
              };
              addCompanyLog('System', `Project paused due to rate limit. User chose to keep current model and wait.`, 'info');
              addDecisionLogEntry('User', 'Project Paused (Rate Limit)', `User opted to pause project and wait for rate limit to resolve for model ${project.rateLimitInfo?.modelId}.`);
              if (prev.id) handleSaveCurrentProject(updatedState);
              return updatedState;
            });
          }}
        />
      )}

      {showDeleteConfirmModal && savedProjects.find(p=>p.id === showDeleteConfirmModal) && (
          <ConfirmModal 
              title="Delete Project"
              message={`Are you sure you want to permanently delete the project "${savedProjects.find(p=>p.id === showDeleteConfirmModal)?.name || 'this project'}"? This action cannot be undone.`}
              onConfirm={() => handleDeleteProject(showDeleteConfirmModal)}
              onCancel={() => setShowDeleteConfirmModal(null)}
              confirmText="Delete Project"
          />
      )}


      <header className="bg-gray-800 shadow-md p-3 flex items-center justify-between sticky top-0 z-30">
        <div className="flex items-center">
          <h1 className="text-xl font-bold text-purple-400 mr-3">DevGenius Studio</h1>
          {project.id && <span className="text-sm text-gray-400 truncate max-w-xs" title={project.name}>{project.name || "Untitled Project"}</span>}
        </div>
        <div className="flex items-center gap-3">
         {project.id && (
            <button
              onClick={handleExportProject}
              className="text-xs bg-blue-600 hover:bg-blue-700 text-white font-semibold py-1.5 px-3 rounded-md transition duration-150 ease-in-out"
            >
              Export Project ZIP
            </button>
          )}
          <button
            onClick={() => {
                const currentApiKey = project.apiKey;
                const currentAgentConfig = project.agentModelConfiguration;
                const currentModels = project.availableModels;
                setProject({ 
                    ...initialProjectContext, 
                    apiKey: currentApiKey,
                    agentModelConfiguration: currentAgentConfig,
                    availableModels: currentModels,
                    currentPhase: currentApiKey ? OperatingPhase.COMPANY_OPERATIONAL : OperatingPhase.AGENT_MODEL_CONFIGURATION,
                    projectLifecycle: currentApiKey ? ProjectLifecyclePhase.AWAITING_PROJECT_IDEA : ProjectLifecyclePhase.IDLE,
                });
                 const storedAuthorship = localStorage.getItem(LOCAL_STORAGE_USER_AUTHORSHIP_KEY);
                 setUserAuthorshipDetails(storedAuthorship ? JSON.parse(storedAuthorship) : null);
                 loadSavedProjects(); // Refresh project list from backend
            }}
            className="text-xs bg-gray-600 hover:bg-gray-700 text-white font-semibold py-1.5 px-3 rounded-md transition duration-150 ease-in-out"
          >
            New/Load Project
          </button>
        </div>
      </header>

    {project.id && ( 
      <div className="p-3">
        <ProjectProgressBar progress={progressPercentage} tasksCompleted={completedTasks} tasksTotal={totalTasks} />
        {project.projectLifecycle === ProjectLifecyclePhase.EXECUTION_HALTED_ERROR && project.errorMessage && (
            <div className="mt-2 p-3 bg-red-700 text-red-100 rounded-md shadow-lg text-sm" role="alert">
              <strong>Execution Halted:</strong> {project.errorMessage}
            </div>
        )}
        {project.projectLifecycle === ProjectLifecyclePhase.PROJECT_PAUSED_ON_RATE_LIMIT && project.rateLimitInfo && (
             <div className="mt-2 p-3 bg-yellow-700 text-yellow-100 rounded-md shadow-lg text-sm" role="alert">
               <strong>Project Paused:</strong> Rate limit encountered for {getAgentDisplayName(project.rateLimitInfo.agentType)} on model {project.availableModels.find(m=>m.id===project.rateLimitInfo?.modelId)?.name || project.rateLimitInfo.modelId}. You can resume if the limit has passed or reconfigure models.
            </div>
        )}
      </div>
    )}

    {project.id ? (
        <ResizablePanels storageKey="devgenius-main-layout" initialSizes={[25, 50, 25]} direction="horizontal" minPanelSizePercent={15}>
          <div className="panel-content p-3 space-y-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-850">
            <FileExplorer files={project.fileStructure} onFileSelect={handleFileSelect} />
            <ArchitecturalInsightsDisplay 
              suggestedTechnologyStack={project.suggestedTechnologyStack}
              architecturalSuggestions={project.architecturalSuggestions}
              architecturalNotes={project.architecturalNotes}
              devNotes={project.devNotes}
            />
          </div>

          <div className="panel-content p-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-850">
            <CodeViewer file={project.currentFilePreview} />
          </div>

          <div className="panel-content p-3 space-y-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-850">
            <TaskListDisplay 
                tasks={project.tasks} 
                activeTaskId={project.activeTaskId} 
                fileStructure={project.fileStructure} 
                onFileSelect={handleFileSelect}
                activeAgentAndModelString={activeAgentAndModelString}
            />
            <AgentLogFeed logs={project.companyLogs} />
            <DecisionLogDisplay decisionLog={project.decisionLog} />

            {(project.projectLifecycle === ProjectLifecyclePhase.PROJECT_VERIFIED || 
              project.projectLifecycle === ProjectLifecyclePhase.PROJECT_READY_FOR_DEPLOYMENT) && (
                <UserFeedbackForm onSubmit={handleUserFeedbackSubmit} isLoading={isFeedbackLoading} />
            )}
          </div>
        </ResizablePanels>
      ) : (
         <div className="flex-grow flex items-center justify-center">
            <LoadingSpinner />
         </div>
      )}
    </div>
  );
}