import { GeminiService } from '../services/geminiService';
import { GeminiJsonBugReportResponse, FileNode } from '../types';

export class BugHunterAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("BugHunterAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Analyzes code for potential bugs and issues.
   * @param code - The source code to analyze.
   * @param filePath - The path of the file being analyzed.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the bug report response.
   */
  public async analyzeCodeForBugs(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugReportResponse> {
    try {
      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        File Path Being Analyzed: ${filePath}
        Code to analyze:
        \`\`\`
        ${code}
        \`\`\`
        Identify potential bugs or issues. Each bug must have a unique 'bugId'.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Bug Hunter Agent. Analyze the provided code for potential bugs and issues. Return a JSON object with 'bugs' array containing bug objects with 'filePath', 'bugId', 'description', and 'severity' properties.",
        (data: any): data is GeminiJsonBugReportResponse => {
          return typeof data === 'object' && data !== null &&
                 'bugs' in data && Array.isArray(data.bugs) &&
                 data.bugs.every((b: any) =>
                   typeof b === 'object' && b !== null &&
                   'filePath' in b && typeof b.filePath === 'string' &&
                   'bugId' in b && typeof b.bugId === 'string' &&
                   'description' in b && typeof b.description === 'string' &&
                   'severity' in b && typeof b.severity === 'string' &&
                   ['low', 'medium', 'high', 'critical'].includes(b.severity)
                 );
        },
        0.4
      );

      return response;
    } catch (error) {
      console.error(`BugHunterAgent: Error analyzing code for bugs in "${filePath}" -`, error);
      throw error;
    }
  }
}
