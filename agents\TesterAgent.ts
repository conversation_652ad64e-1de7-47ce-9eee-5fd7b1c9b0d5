import { GeminiService } from '../services/geminiService';
import {
  GeminiJsonTestPlanResponse,
  GeminiJsonCodeResponse,
  GeminiJsonTestAnalysisResponse,
  GeminiJsonSingleTestFailureAnalysisResponse,
  FileNode,
  BugInfo
} from '../types';

export class TesterAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("TesterAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Generates a comprehensive test plan for the project.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the test plan response.
   */
  public async generateTestPlan(
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonTestPlanResponse> {
    try {
      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        Based on the project context and existing source files, devise a test plan.
        Specify test files to create, their purpose, and related source files.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Test Planning Agent. Generate a comprehensive test plan for the given project. Return a JSON object with 'testFiles' array containing objects with 'filePath', 'description', and 'relatedSourceFiles' properties.",
        (data: any): data is GeminiJsonTestPlanResponse => {
          return typeof data === 'object' && data !== null &&
                 'testFiles' in data && Array.isArray(data.testFiles) &&
                 data.testFiles.every((tf: any) =>
                   typeof tf === 'object' && tf !== null &&
                   'filePath' in tf && typeof tf.filePath === 'string' &&
                   'description' in tf && typeof tf.description === 'string' &&
                   'relatedSourceFiles' in tf && Array.isArray(tf.relatedSourceFiles) &&
                   tf.relatedSourceFiles.every((rsf: any) => typeof rsf === 'string')
                 );
        },
        0.4
      );

      return response;
    } catch (error) {
      console.error("TesterAgent: Error generating test plan -", error);
      throw error;
    }
  }

  /**
   * Generates test code for a specific test file.
   * @param projectContext - The current project context.
   * @param testFilePath - The path of the test file to generate.
   * @param testFileDescription - Description of what the test file should test.
   * @param relatedSourceFiles - Array of related source file paths.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the generated test code response.
   */
  public async generateTestCode(
    projectContext: string,
    testFilePath: string,
    testFileDescription: string,
    relatedSourceFiles: string[],
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonCodeResponse> {
    try {
      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        Test File Path To Generate: ${testFilePath}
        Test File Description/Purpose: ${testFileDescription}
        Related Source Files: ${relatedSourceFiles.join(', ')}
        Generate the test code for this file.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Test Code Generator Agent. Generate comprehensive test code for the specified test file. Return a JSON object with 'code' property containing the test code.",
        (data: any): data is GeminiJsonCodeResponse => {
          return typeof data === 'object' && data !== null &&
                 'code' in data && typeof data.code === 'string';
        },
        0.5,
        true
      );

      return response;
    } catch (error) {
      console.error(`TesterAgent: Error generating test code for "${testFilePath}" -`, error);
      throw error;
    }
  }

  /**
   * Analyzes the project by running tests and identifying issues.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the test analysis response.
   */
  public async analyzeProjectWithTests(
    projectContext: string,
    fileStructure: FileNode[],
    _modelName: string
  ): Promise<GeminiJsonTestAnalysisResponse> {
    try {
      console.log("TesterAgent: Initiating actual test execution (Test Runner)...");
      const issues: BugInfo[] = [];
      let overallSummary = "Test Execution Summary (Local Runner):\n";
      let testsRun = 0;
      let testsFailed = 0;

      // Get test files from file structure
      const testFileNodes = fileStructure.flatMap(node => {
          const collectTestFiles = (n: FileNode): FileNode[] => {
              let files: FileNode[] = [];
              if (n.type === 'file' && n.isTestFile && n.content) {
                  files.push(n);
              }
              if (n.children) {
                  files = files.concat(...n.children.map(collectTestFiles));
              }
              return files;
          };
          return collectTestFiles(node);
      });

      // Create source files map
      const sourceFilesMap: Record<string, FileNode> = {};
      fileStructure.forEach(node => {
          const traverse = (n: FileNode) => {
              if (n.type === 'file' && n.content) {
                  sourceFilesMap[n.path] = n;
              }
              if (n.children) n.children.forEach(traverse);
          };
          traverse(node);
      });

      // Run tests for each test file
      for (const testFileNode of testFileNodes) {
          if (!testFileNode.content) continue;

          console.log(`TesterAgent: Executing tests in: ${testFileNode.path}`);
          try {
              const testResults = await this.runTestCode(testFileNode.content, sourceFilesMap, testFileNode.path);

              overallSummary += `\n--- Test File: ${testFileNode.path} ---\n`;
              testResults.forEach(result => {
                  testsRun++;
                  overallSummary += `  ${result.passed ? '✅' : '❌'} [${result.suiteName}] ${result.testName} (${result.duration.toFixed(2)}ms)\n`;
                  if (!result.passed) {
                      testsFailed++;
                      const errorMsg = result.error?.message || "Unknown error";
                      overallSummary += `     Error: ${errorMsg}\n`;
                      if (result.error?.stack) {
                           overallSummary += `     Stack: ${result.error.stack.substring(0, 200)}...\n`;
                      }

                      issues.push({
                          filePath: testFileNode.path,
                          bugId: `test-run-fail-${Date.now().toString(36)}`,
                          description: `Test Failed: [${result.suiteName}] "${result.testName}". Error: ${errorMsg}`,
                          severity: 'high',
                          attempts: 0,
                          isSecurityIssue: false,
                          originalTestFailure: {
                              testFilePath: testFileNode.path,
                              testName: `[${result.suiteName}] ${result.testName}`,
                              errorMessage: errorMsg + (result.error?.stack ? `\nStack: ${result.error.stack}` : '')
                          }
                      });
                  }
              });
          } catch (e: any) {
              console.error(`TesterAgent: Critical error running test file ${testFileNode.path}:`, e);
              testsRun++;
              testsFailed++;
              const criticalErrorMsg = e.message || String(e);
              issues.push({
                  filePath: testFileNode.path,
                  bugId: `test-execution-error-${Date.now().toString(36)}`,
                  description: `Failed to execute test file ${testFileNode.path}: ${criticalErrorMsg}`,
                  severity: 'high',
                  attempts: 0,
                  isSecurityIssue: false,
                  originalTestFailure: {
                      testFilePath: testFileNode.path,
                      testName: "Test File Execution Error",
                      errorMessage: criticalErrorMsg + (e.stack ? `\nStack: ${e.stack}` : '')
                  }
              });
              overallSummary += `  ❌ FATAL ERROR executing ${testFileNode.path}: ${criticalErrorMsg}\n`;
          }
      }

      overallSummary += `\nTotal Tests Run: ${testsRun}. Passed: ${testsRun - testsFailed}. Failed: ${testsFailed}.\n`;
      console.log("TesterAgent: Test Runner Summary:", overallSummary);

      return {
          issues: issues,
          summary: overallSummary,
      };
    } catch (error) {
      console.error("TesterAgent: Error analyzing project with tests -", error);
      throw error;
    }
  }

  /**
   * Analyzes a single test failure to provide detailed bug information.
   * @param failedTestDetails - The details of the failed test.
   * @param testFileNode - The test file node containing the test code.
   * @param fileStructure - The current file structure of the project.
   * @param projectFullContext - The full project context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the single test failure analysis response.
   */
  public async analyzeSingleTestFailure(
    failedTestDetails: BugInfo,
    testFileNode: FileNode,
    fileStructure: FileNode[],
    projectFullContext: string,
    modelName: string
  ): Promise<GeminiJsonSingleTestFailureAnalysisResponse> {
    try {
      if (!failedTestDetails.originalTestFailure) {
          throw new Error("Original test failure details are missing in BugInfo for AI analysis.");
      }
      if (!testFileNode || !testFileNode.content) {
          throw new Error(`Test file node or its content is missing for path: ${failedTestDetails.originalTestFailure.testFilePath}`);
      }

      const relatedSourceFilesContent: { path: string, content: string }[] = [];
      const testFileNameNoExt = testFileNode.name.split('.')[0].toLowerCase();

      fileStructure.forEach(sf => {
          if (sf.content && !sf.isTestFile) {
              if (sf.name.toLowerCase().includes(testFileNameNoExt) || testFileNameNoExt.includes(sf.name.split('.')[0].toLowerCase()) ) {
                   relatedSourceFilesContent.push({ path: sf.path, content: sf.content });
              }
              else if (testFileNode.content && (testFileNode.content.includes(`'${sf.name}'`) || testFileNode.content.includes(`"${sf.name}"`) || testFileNode.content.includes(sf.name.split('.')[0]) )) {
                   relatedSourceFilesContent.push({ path: sf.path, content: sf.content });
              }
          }
      });

      if (relatedSourceFilesContent.length === 0) {
          fileStructure.filter(sf => !sf.isTestFile && sf.content).slice(0, 3).forEach(sf => {
              relatedSourceFilesContent.push({ path: sf.path, content: sf.content! });
          });
      }

      const sourceCodePrompt = relatedSourceFilesContent.map(sf => `--- Source Code: ${sf.path} ---\n${sf.content}\n--- End Source Code: ${sf.path} ---`).join('\n\n');

      const originalPrompt = `
        Project Full Context: ${projectFullContext}
        ---
        Test Failure Details:
        Test File Path: ${failedTestDetails.originalTestFailure.testFilePath}
        Test Name: ${failedTestDetails.originalTestFailure.testName}
        Error Message & Stack (if any): ${failedTestDetails.originalTestFailure.errorMessage}
        ---
        Full Code of the Test File (${testFileNode.path}):
        \`\`\`javascript
        ${testFileNode.content}
        \`\`\`
        ---
        Relevant Source Code Snippets (from files like ${relatedSourceFilesContent.map(sf => sf.path).join(', ') || 'N/A'}):
        ${sourceCodePrompt || "No specific related source files identified; consider general project context and provided file structure."}
        ---
        Analyze this failed test and provide a BugInfo object.
        The "filePath" in BugInfo MUST be the path to the SOURCE file where the bug most likely resides.
        The "description" should explain the root cause of the bug in the source code, not just repeat the test error.
        The "bugId" should be a new unique ID prefixed with "test-analysis-".
        Include "lineNumber" in BugInfo if you can identify it in the source file.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Test Failure Analyzer Agent. Analyze the failed test and identify the root cause in the source code. Return a JSON object with 'analyzedBugInfo' containing the bug details.",
        (data: any): data is GeminiJsonSingleTestFailureAnalysisResponse => {
          return typeof data === 'object' && data !== null &&
                 'analyzedBugInfo' in data && typeof data.analyzedBugInfo === 'object' && data.analyzedBugInfo !== null &&
                 'filePath' in data.analyzedBugInfo && typeof data.analyzedBugInfo.filePath === 'string' &&
                 'bugId' in data.analyzedBugInfo && typeof data.analyzedBugInfo.bugId === 'string' &&
                 'description' in data.analyzedBugInfo && typeof data.analyzedBugInfo.description === 'string' &&
                 'severity' in data.analyzedBugInfo && typeof data.analyzedBugInfo.severity === 'string' &&
                 ['low', 'medium', 'high', 'critical'].includes(data.analyzedBugInfo.severity);
        },
        0.5
      );

      return response;
    } catch (error) {
      console.error("TesterAgent: Error analyzing single test failure -", error);
      throw error;
    }
  }

  /**
   * Analyzes test coverage and suggests additional tests if needed.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the test coverage analysis response.
   */
  public async analyzeTestCoverage(
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonTestPlanResponse> {
    try {
      const fileStructureWithContentPrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure, true);

      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructureWithContentPrompt}
        Based on the project context, existing source code (including its content), and existing test files (including their content), analyze the current test coverage.
        Identify critical untested areas of the source code. Suggest a few (0 to 3 MAXIMUM) new test files to improve coverage for these critical areas.
        If coverage is deemed reasonably sufficient for the project's complexity and critical paths (considering the content of existing tests), return an empty "testFiles" array.
        Each suggested test file object MUST have "filePath", "description" (why it's important for coverage), and "relatedSourceFiles".
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Test Coverage Analyzer Agent. Analyze the current test coverage and suggest additional tests if needed. Return a JSON object with 'testFiles' array.",
        (data: any): data is GeminiJsonTestPlanResponse => {
          return typeof data === 'object' && data !== null &&
                 'testFiles' in data && Array.isArray(data.testFiles) &&
                 data.testFiles.every((tf: any) =>
                   typeof tf === 'object' && tf !== null &&
                   'filePath' in tf && typeof tf.filePath === 'string' &&
                   'description' in tf && typeof tf.description === 'string' &&
                   'relatedSourceFiles' in tf && Array.isArray(tf.relatedSourceFiles) &&
                   tf.relatedSourceFiles.every((rsf: any) => typeof rsf === 'string')
                 );
        },
        0.4
      );

      return response;
    } catch (error) {
      console.error("TesterAgent: Error analyzing test coverage -", error);
      throw error;
    }
  }

  /**
   * Runs test code in a browser environment and returns test results.
   * @param testFileContent - The content of the test file to execute.
   * @param sourceFilesMap - Map of source file paths to FileNode objects.
   * @param testFilePath - The path of the test file being executed.
   * @returns A promise that resolves to an array of test results.
   */
  public async runTestCode(
    testFileContent: string,
    sourceFilesMap: Record<string, FileNode>,
    testFilePath: string
  ): Promise<any[]> {
    try {
      // Delegate to the GeminiService's runTestCode method
      return await (this.geminiService as any).runTestCode(
        testFileContent,
        sourceFilesMap,
        testFilePath
      );
    } catch (error) {
      console.error(`TesterAgent: Error running test code for "${testFilePath}" -`, error);
      throw error;
    }
  }
}
