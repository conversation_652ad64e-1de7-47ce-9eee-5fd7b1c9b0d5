import { GeminiService } from '../services/geminiService';

export class ImageGeneratorAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("ImageGeneratorAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Generates an image based on a text prompt using the Gemini image generation model.
   * @param prompt - The text prompt describing the image to generate.
   * @param modelName - The name of the Gemini image generation model to use.
   * @returns A promise that resolves to the generated image data with dataUrl, rawBytes, and mimeType.
   */
  public async generateImage(
    prompt: string,
    modelName: string,
    outputMimeType: 'image/png' | 'image/jpeg' = 'image/png'
  ): Promise<{ dataUrl: string, rawBytes: string, mimeType: string }> {
    try {
      // Since the original generateImageViaGemini method was removed, we need to implement it here
      let currentAttempt = 0;
      let lastError: Error | null = null;
      const maxRetries = 3;

      while (currentAttempt <= maxRetries) {
          try {
              if (currentAttempt > 0 && lastError) {
                   console.warn(`ImageGeneratorAgent: Retry ${currentAttempt}/${maxRetries} for image generation using model ${modelName} due to error: ${lastError.message}. Retrying.`);
              }

              const config: {
                numberOfImages: number;
                outputMimeType: 'image/png' | 'image/jpeg';
              } = {
                numberOfImages: 1,
                outputMimeType: outputMimeType
              };

              console.log(`ImageGeneratorAgent: Attempting image generation (attempt ${currentAttempt + 1}/${maxRetries + 1}) for model ${modelName} with prompt: "${prompt.substring(0,100)}..."`);

              const response = await (this.geminiService as any).ai.models.generateImages({
                  model: modelName,
                  prompt: prompt,
                  config: config,
              });

              if (!response.generatedImages || response.generatedImages.length === 0 || !response.generatedImages[0].image?.imageBytes) {
                  console.error("ImageGeneratorAgent: Gemini image generation response missing image data. Full response:", JSON.stringify(response).substring(0, 500));
                  throw new Error('Image generation failed: No image data received from API.');
              }

              const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
              const dataUrl = `data:${outputMimeType};base64,${base64ImageBytes}`;

              console.log(`ImageGeneratorAgent: Image generation for model ${modelName} complete. Image bytes length: ${base64ImageBytes?.length || 0}`);
              return { dataUrl, rawBytes: base64ImageBytes, mimeType: outputMimeType };

          } catch (error) {
              const err = error instanceof Error ? error : new Error(String(error));
              console.error(`ImageGeneratorAgent: Image generation attempt ${currentAttempt + 1}/${maxRetries + 1} with model ${modelName} failed:`, err.message);
              lastError = err;
          }

          currentAttempt++;
          if (currentAttempt <= maxRetries) {
              const delay = Math.min(5000, 1000 * Math.pow(2, currentAttempt - 1));
              console.log(`ImageGeneratorAgent: Waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetries + 1}.`);
              await new Promise(resolve => setTimeout(resolve, delay));
          }
      }

      throw lastError || new Error(`ImageGeneratorAgent: Image generation failed for model ${modelName} after exhausting all ${maxRetries + 1} retries. Unknown error.`);
    } catch (error) {
      console.error(`ImageGeneratorAgent: Error generating image for prompt "${prompt.substring(0, 50)}..." -`, error);
      throw error;
    }
  }
}
