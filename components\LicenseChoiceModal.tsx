
import React from 'react';

interface LicenseChoiceModalProps {
  isOpen: boolean;
  onClose: () => void; // Might not be used if selection is mandatory to proceed
  onSubmit: (choice: 'open-source' | 'proprietary') => void;
  hasExistingAuthorship: boolean;
}

export const LicenseChoiceModal: React.FC<LicenseChoiceModalProps> = ({ isOpen, _onClose, onSubmit, hasExistingAuthorship }) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div 
      className="fixed inset-0 bg-gray-900 bg-opacity-80 flex items-center justify-center p-4 z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="license-choice-modal-title"
    >
      <div className="bg-gray-800 p-6 sm:p-8 rounded-lg shadow-2xl w-full max-w-lg border border-purple-600">
        <h2 id="license-choice-modal-title" className="text-xl sm:text-2xl font-bold mb-4 text-center text-purple-400">Choose Your Project License</h2>
        <p className="text-sm text-gray-300 mb-6 text-center">
          Select a license for your project. This will determine how others can use your code.
        </p>

        <div className="space-y-4">
          <button
            onClick={() => onSubmit('open-source')}
            className="w-full flex flex-col items-center justify-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg border border-gray-600 hover:border-purple-500 transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500"
            aria-label="Select Open Source (MIT License)"
          >
            <h3 className="text-lg font-semibold text-green-400">Open Source (MIT License)</h3>
            <p className="text-xs text-gray-400 mt-1 text-center">
              Permissive license allowing broad use, modification, and distribution, including for commercial purposes, as long as the original copyright and license notice are included.
            </p>
          </button>

          <button
            onClick={() => onSubmit('proprietary')}
            className="w-full flex flex-col items-center justify-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg border border-gray-600 hover:border-purple-500 transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500"
            aria-label="Select Proprietary License"
          >
            <h3 className="text-lg font-semibold text-orange-400">Proprietary License</h3>
            <p className="text-xs text-gray-400 mt-1 text-center">
              Retain full ownership and control over your code. Typically restricts use, modification, and distribution without explicit permission.
              {hasExistingAuthorship ? " Stored authorship details will be used." : " You'll be asked for authorship details."}
            </p>
          </button>
        </div>
        
        <p className="text-xs text-gray-500 mt-6 text-center">
          Choosing a license is an important step. You can learn more about software licenses online.
        </p>
        {/* onClose might be implemented if there's a scenario where user can dismiss this without choosing
            e.g., <button onClick={onClose} className="mt-4 text-xs text-gray-400 hover:underline">Decide Later (Not Recommended)</button> 
            But current flow seems to require a choice to proceed.
        */}
      </div>
    </div>
  );
};
